/* Upload Progress Bar Styles */
.upload-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.upload-progress-container {
  background: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--heading4);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 500px;
  width: 90%;
  text-align: center;
}

.upload-progress-header {
  margin-bottom: var(--heading5);
}

.upload-progress-title {
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--secondary-color);
  margin: 0 0 var(--smallfont) 0;
}

.upload-progress-filename {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0;
  word-break: break-all;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.upload-progress-bar-container {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  margin-bottom: var(--heading5);
}

.upload-progress-bar {
  flex: 1;
  height: 12px;
  background-color: var(--light-gray);
  border-radius: 6px;
  overflow: hidden;
  position: relative;
}

.upload-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--btn-color), var(--primary-color));
  border-radius: 6px;
  transition: width 0.3s ease;
  position: relative;
}

.upload-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.upload-progress-percentage {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
  min-width: 40px;
  text-align: right;
}

.upload-progress-status {
  margin-top: var(--basefont);
}

.upload-progress-message {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0;
  line-height: 1.5;
}

.upload-progress-complete {
  color: var(--btn-color);
  font-weight: 500;
}

/* Enhanced Upload Progress Styles */
.upload-progress-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--smallfont);
}

.upload-progress-controls {
  display: flex;
  gap: var(--smallfont);
}

.upload-control-btn {
  background: none;
  border: none;
  padding: var(--extrasmallfont);
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
}

.upload-cancel-btn {
  color: var(--error-color, #dc3545);
  background-color: rgba(220, 53, 69, 0.1);
}

.upload-cancel-btn:hover {
  background-color: rgba(220, 53, 69, 0.2);
  transform: scale(1.1);
}

/* Progress Bar States */
.upload-progress-error {
  background: linear-gradient(90deg, #dc3545, #c82333);
}

.upload-progress-paused {
  background: linear-gradient(90deg, #ffc107, #e0a800);
}

/* Upload Statistics */
.upload-progress-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--smallfont);
  margin: var(--basefont) 0;
  padding: var(--basefont);
  background-color: var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
}

.upload-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-stat-label {
  color: var(--dark-gray);
  font-weight: 500;
}

.upload-stat-value {
  color: var(--secondary-color);
  font-weight: 600;
}

/* Error Section */
.upload-error-section {
  text-align: center;
}

.upload-progress-error {
  color: var(--error-color, #dc3545);
  font-weight: 500;
}

.upload-retry-btn {
  background: var(--btn-color);
  color: var(--white);
  border: none;
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--smallfont);
  font-weight: 500;
  margin-top: var(--smallfont);
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  transition: all 0.2s ease;
}

.upload-retry-btn:hover:not(:disabled) {
  background: var(--primary-color);
  transform: translateY(-1px);
}

.upload-retry-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Pause/Resume Section */
.upload-pause-section,
.upload-active-section {
  text-align: center;
}

.upload-pause-btn,
.upload-resume-btn {
  background: var(--secondary-color);
  color: var(--white);
  border: none;
  padding: var(--extrasmallfont) var(--smallfont);
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: var(--extrasmallfont);
  margin-top: var(--extrasmallfont);
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
}

.upload-resume-btn {
  background: var(--btn-color);
}

.upload-pause-btn:hover,
.upload-resume-btn:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}

/* Spinning animation for retry icon */
.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .upload-progress-container {
    padding: var(--heading5);
    max-width: 400px;
  }

  .upload-progress-title {
    font-size: var(--basefont);
  }

  .upload-progress-filename {
    font-size: var(--extrasmallfont);
  }

  .upload-progress-bar-container {
    gap: var(--smallfont);
  }

  .upload-progress-bar {
    height: 10px;
  }

  .upload-progress-percentage {
    font-size: var(--extrasmallfont);
    min-width: 35px;
  }

  .upload-progress-message {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .upload-progress-container {
    padding: var(--basefont);
    max-width: 350px;
  }

  .upload-progress-title {
    font-size: var(--smallfont);
  }

  .upload-progress-bar {
    height: 8px;
  }

  .upload-progress-percentage {
    font-size: var(--extrasmallfont);
    min-width: 30px;
  }
}
