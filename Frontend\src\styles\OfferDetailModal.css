.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-content .modal-header {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: sticky;
  top: 0;
  background: white;
  z-index: 1;
}

.modal-content .modal-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.5rem;
}

.modal-content .close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.modal-content .close-button:hover {
  background: #f5f5f5;
  color: #333;
}

.modal-content .modal-body {
  padding: 20px;
}

.modal-content .detail-section {
  margin-bottom: 24px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--light-gray);
}

.modal-content .detail-section h3 {
  color: #333;
  font-size: 1.2rem;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #f0f0f0;
}

.modal-content .detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.modal-content .detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.modal-content .detail-item.full-width {
  grid-column: 1 / -1;
  margin-top: 16px;
}

.modal-content .detail-item label {
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

.modal-content .detail-item span {
  color: #333;
  font-size: 1rem;
}

.modal-content .detail-item span.highlight {
  font-size: 1.2rem;
  font-weight: 600;
  color: #28a745;
}

.modal-content .content-description {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  margin-top: 8px;
}

.modal-content .content-description p {
  margin: 0;
  line-height: 1.5;
}

/* User Profile Styles */
.modal-content .user-profile {
  display: flex;
  gap: 20px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  align-items: center;
}

.modal-content .user-avatar {
  flex-shrink: 0;
}

.modal-content .user-avatar .profile-image {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.modal-content .user-details {
  flex-grow: 1;
}

.modal-content .user-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.modal-content .user-header .name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.modal-content .user-contact {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.modal-content .contact-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
}

.modal-content .contact-item .icon {
  font-size: 14px;
  color: #666;
}

.modal-content .message-box {
  background: #f8f9fa;
  padding: 16px;
  border-radius: 6px;
  color: #333;
  font-size: 1rem;
  line-height: 1.5;
  white-space: pre-wrap;
  margin-top: 8px;
}

.modal-content .message-box + .message-box {
  margin-top: 16px;
}

.modal-content .message-box.seller-response {
  background: #e8f4ff;
  border-left: 4px solid #0d6efd;
}

.modal-content .message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #666;
  font-weight: 500;
  font-size: 0.9rem;
}

.modal-content .message-header .icon {
  font-size: 14px;
}

.modal-content .seller-response .message-header {
  color: #0d6efd;
}

.modal-content .modal-footer {
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  position: sticky;
  bottom: 0;
  background: white;
}

.modal-content .action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s;
}

.modal-content .action-btn svg {
  font-size: 16px;
}

.modal-content .action-btn.approve {
  background: #28a745;
  color: white;
}

.modal-content .action-btn.reject {
  background: #ffc107;
  color: #000;
}

.modal-content .action-btn.delete {
  background: #dc3545;
  color: white;
}

.modal-content .action-btn.cancel {
  background: #6c757d;
  color: white;
}

.modal-content .action-btn:hover {
  opacity: 0.9;
}

/* Status badge styles */
.modal-content .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
  font-weight: 500;
}

.modal-content .status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.modal-content .status-badge.accepted {
  background: #d4edda;
  color: #155724;
}

.modal-content .status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

.modal-content .status-badge.expired {
  background: #e2e3e5;
  color: #383d41;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .modal-content {
    width: 95%;
    max-height: 95vh;
  }

  .modal-content .detail-grid {
    grid-template-columns: 1fr;
  }

  .modal-content .user-profile {
    flex-direction: column;
    text-align: center;
  }

  .modal-content .user-contact {
    align-items: center;
  }

  .modal-content .modal-footer {
    flex-wrap: wrap;
  }

  .modal-content .action-btn {
    flex: 1;
    justify-content: center;
  }
}
ul {
  list-style-position: inside;
}
