/**
 * Upload Configuration
 * Centralized configuration for file upload settings
 */

// File size thresholds for different upload strategies
export const UPLOAD_THRESHOLDS = {
  // Use chunked upload for files larger than this size
  CHUNKED_UPLOAD_THRESHOLD: 100 * 1024 * 1024, // 100MB
  
  // Use standard upload for files smaller than this size
  STANDARD_UPLOAD_THRESHOLD: 50 * 1024 * 1024, // 50MB
  
  // Show enhanced progress for files larger than this size
  ENHANCED_PROGRESS_THRESHOLD: 10 * 1024 * 1024, // 10MB
};

// Chunked upload settings
export const CHUNKED_UPLOAD_CONFIG = {
  // Size of each chunk
  CHUNK_SIZE: 5 * 1024 * 1024, // 5MB
  
  // Maximum number of retry attempts per chunk
  MAX_RETRIES: 3,
  
  // Base delay for retry attempts (exponential backoff)
  RETRY_DELAY_BASE: 1000, // 1 second
  
  // Maximum concurrent chunk uploads
  CONCURRENCY_LIMIT: 2,
  
  // Timeout for individual chunk uploads
  CHUNK_TIMEOUT: 300000, // 5 minutes
};

// Standard upload settings
export const STANDARD_UPLOAD_CONFIG = {
  // Timeout for standard uploads
  UPLOAD_TIMEOUT: 3600000, // 1 hour
  
  // Progress update interval
  PROGRESS_UPDATE_INTERVAL: 100, // milliseconds
};

// Upload progress settings
export const PROGRESS_CONFIG = {
  // Update frequency for progress indicators
  UPDATE_FREQUENCY: 500, // milliseconds
  
  // Speed calculation window (for averaging)
  SPEED_CALCULATION_WINDOW: 5000, // 5 seconds
  
  // ETA calculation smoothing factor
  ETA_SMOOTHING_FACTOR: 0.1,
};

// Error handling settings
export const ERROR_CONFIG = {
  // Network error retry attempts
  NETWORK_RETRY_ATTEMPTS: 3,
  
  // Timeout error retry attempts
  TIMEOUT_RETRY_ATTEMPTS: 2,
  
  // Server error retry attempts
  SERVER_ERROR_RETRY_ATTEMPTS: 1,
  
  // Retry delay for different error types
  RETRY_DELAYS: {
    NETWORK_ERROR: 2000, // 2 seconds
    TIMEOUT_ERROR: 5000, // 5 seconds
    SERVER_ERROR: 10000, // 10 seconds
  },
};

// File type specific settings
export const FILE_TYPE_CONFIG = {
  VIDEO: {
    // Always use chunked upload for videos
    FORCE_CHUNKED: true,
    
    // Larger chunk size for videos
    CHUNK_SIZE: 10 * 1024 * 1024, // 10MB
    
    // Extended timeout for video processing
    TIMEOUT: 7200000, // 2 hours
  },
  
  DOCUMENT: {
    // Use standard upload for documents
    FORCE_CHUNKED: false,
    
    // Standard chunk size
    CHUNK_SIZE: 5 * 1024 * 1024, // 5MB
    
    // Standard timeout
    TIMEOUT: 1800000, // 30 minutes
  },
  
  IMAGE: {
    // Never use chunked upload for images
    FORCE_STANDARD: true,
    
    // Standard timeout
    TIMEOUT: 600000, // 10 minutes
  },
};

// Browser compatibility settings
export const BROWSER_CONFIG = {
  // Browsers that support chunked uploads
  SUPPORTED_BROWSERS: [
    'Chrome',
    'Firefox',
    'Safari',
    'Edge',
  ],
  
  // Fallback settings for unsupported browsers
  FALLBACK_CONFIG: {
    MAX_FILE_SIZE: 500 * 1024 * 1024, // 500MB
    CHUNK_SIZE: 2 * 1024 * 1024, // 2MB
    TIMEOUT: 1800000, // 30 minutes
  },
};

// Performance optimization settings
export const PERFORMANCE_CONFIG = {
  // Memory usage limits
  MAX_MEMORY_USAGE: 100 * 1024 * 1024, // 100MB
  
  // Garbage collection frequency
  GC_FREQUENCY: 10, // Every 10 chunks
  
  // Buffer size for file reading
  BUFFER_SIZE: 64 * 1024, // 64KB
  
  // Connection pooling settings
  CONNECTION_POOL: {
    MAX_CONNECTIONS: 4,
    KEEP_ALIVE: true,
    TIMEOUT: 30000, // 30 seconds
  },
};

// Monitoring and analytics settings
export const MONITORING_CONFIG = {
  // Enable upload analytics
  ENABLE_ANALYTICS: true,
  
  // Log upload events
  LOG_EVENTS: true,
  
  // Performance metrics collection
  COLLECT_METRICS: true,
  
  // Error reporting
  REPORT_ERRORS: true,
};

/**
 * Get upload configuration based on file properties
 * @param {File} file - The file to upload
 * @param {string} contentType - The content type (Video, Document, etc.)
 * @returns {Object} Upload configuration
 */
export const getUploadConfig = (file, contentType) => {
  const fileSize = file.size;
  const fileType = contentType || 'DOCUMENT';
  
  // Get file type specific config
  const typeConfig = FILE_TYPE_CONFIG[fileType] || FILE_TYPE_CONFIG.DOCUMENT;
  
  // Determine upload strategy
  let useChunkedUpload = false;
  
  if (typeConfig.FORCE_CHUNKED) {
    useChunkedUpload = true;
  } else if (typeConfig.FORCE_STANDARD) {
    useChunkedUpload = false;
  } else {
    useChunkedUpload = fileSize > UPLOAD_THRESHOLDS.CHUNKED_UPLOAD_THRESHOLD;
  }
  
  return {
    useChunkedUpload,
    chunkSize: typeConfig.CHUNK_SIZE || CHUNKED_UPLOAD_CONFIG.CHUNK_SIZE,
    timeout: typeConfig.TIMEOUT || STANDARD_UPLOAD_CONFIG.UPLOAD_TIMEOUT,
    maxRetries: CHUNKED_UPLOAD_CONFIG.MAX_RETRIES,
    concurrencyLimit: CHUNKED_UPLOAD_CONFIG.CONCURRENCY_LIMIT,
    retryDelayBase: CHUNKED_UPLOAD_CONFIG.RETRY_DELAY_BASE,
  };
};

/**
 * Check if browser supports chunked uploads
 * @returns {boolean} Whether chunked uploads are supported
 */
export const isChunkedUploadSupported = () => {
  // Check for required APIs
  if (!window.File || !window.FileReader || !window.Blob) {
    return false;
  }
  
  // Check for slice method
  if (!File.prototype.slice && !File.prototype.webkitSlice && !File.prototype.mozSlice) {
    return false;
  }
  
  // Check for FormData
  if (!window.FormData) {
    return false;
  }
  
  return true;
};

/**
 * Get optimal chunk size based on file size and connection speed
 * @param {number} fileSize - Size of the file in bytes
 * @param {number} connectionSpeed - Connection speed in bytes per second
 * @returns {number} Optimal chunk size in bytes
 */
export const getOptimalChunkSize = (fileSize, connectionSpeed = null) => {
  // Base chunk size
  let chunkSize = CHUNKED_UPLOAD_CONFIG.CHUNK_SIZE;
  
  // Adjust based on file size
  if (fileSize < 100 * 1024 * 1024) { // < 100MB
    chunkSize = 2 * 1024 * 1024; // 2MB
  } else if (fileSize < 500 * 1024 * 1024) { // < 500MB
    chunkSize = 5 * 1024 * 1024; // 5MB
  } else { // >= 500MB
    chunkSize = 10 * 1024 * 1024; // 10MB
  }
  
  // Adjust based on connection speed if available
  if (connectionSpeed) {
    // Target 30-60 seconds per chunk
    const targetTime = 45; // seconds
    const calculatedSize = connectionSpeed * targetTime;
    
    // Use calculated size if it's within reasonable bounds
    if (calculatedSize >= 1024 * 1024 && calculatedSize <= 20 * 1024 * 1024) {
      chunkSize = calculatedSize;
    }
  }
  
  return Math.round(chunkSize);
};

export default {
  UPLOAD_THRESHOLDS,
  CHUNKED_UPLOAD_CONFIG,
  STANDARD_UPLOAD_CONFIG,
  PROGRESS_CONFIG,
  ERROR_CONFIG,
  FILE_TYPE_CONFIG,
  BROWSER_CONFIG,
  PERFORMANCE_CONFIG,
  MONITORING_CONFIG,
  getUploadConfig,
  isChunkedUploadSupported,
  getOptimalChunkSize,
};
