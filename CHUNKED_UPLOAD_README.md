# Chunked Upload Implementation for Large Video Files

This document describes the implementation of chunked upload functionality to handle large video files (500MB-1GB) reliably in the MERN Sports Hub application.

## Problem Statement

The original implementation had issues with large video uploads (500MB+):
- Network timeout errors during upload
- Browser timeout limitations
- No retry mechanisms for failed uploads
- Memory issues with large files
- No upload progress indicators for long uploads
- Single-point-of-failure uploads

## Solution Overview

The new implementation provides:
1. **Chunked Upload**: Files are split into 5-10MB chunks for reliable upload
2. **Retry Mechanisms**: Failed chunks are automatically retried with exponential backoff
3. **Resume Capability**: Interrupted uploads can be resumed from where they left off
4. **Enhanced Progress UI**: Real-time progress with speed, ETA, and chunk status
5. **Error Handling**: Comprehensive error handling with user-friendly messages
6. **Configurable Settings**: Adaptive chunk sizes based on file type and size

## Architecture

### Backend Components

#### 1. Chunked Upload Routes (`Backend/routes/content.js`)
- `POST /api/content/upload/init` - Initialize upload session
- `POST /api/content/upload/chunk` - Upload individual chunks
- `POST /api/content/upload/complete` - Complete upload and assemble file
- `GET /api/content/upload/status/:uploadId` - Get upload progress

#### 2. Chunk Assembler (`Backend/utils/chunkAssembler.js`)
- Assembles uploaded chunks into final file
- Supports both S3 and local storage
- Handles cleanup of temporary chunks
- Uses S3 multipart upload for cloud storage

#### 3. Upload Configuration (`Backend/config/uploadConfig.js`)
- Centralized upload settings
- File size limits and timeouts
- Supported file types and formats

### Frontend Components

#### 1. Chunked Upload Service (`Frontend/src/services/chunkedUploadService.js`)
- Handles file chunking and upload logic
- Implements retry mechanisms with exponential backoff
- Provides progress tracking and statistics
- Manages upload state and error handling

#### 2. Enhanced Upload Progress Bar (`Frontend/src/components/common/UploadProgressBar.jsx`)
- Real-time progress indicators
- Upload speed and ETA calculations
- Retry and cancel functionality
- Error display with actionable buttons

#### 3. Upload Configuration (`Frontend/src/config/uploadConfig.js`)
- Client-side upload settings
- Browser compatibility checks
- Adaptive chunk size calculation

## Usage

### Automatic Chunked Upload

The system automatically determines when to use chunked upload:

```javascript
// Files > 100MB automatically use chunked upload
const shouldUseChunkedUpload = file.size > 100 * 1024 * 1024;

if (shouldUseChunkedUpload) {
  // Use chunked upload service
  result = await chunkedUploadService.uploadFile(
    file,
    contentType,
    onProgress,
    onError,
    onRetry
  );
} else {
  // Use standard upload
  result = await standardUploadService.uploadFile(file);
}
```

### Manual Chunked Upload

```javascript
import chunkedUploadService from '../services/chunkedUploadService';

const uploadFile = async (file) => {
  try {
    const result = await chunkedUploadService.uploadFile(
      file,
      'Video', // Content type
      (stats) => {
        // Progress callback
        console.log(`Progress: ${stats.progress}%`);
        console.log(`Speed: ${stats.speed} bytes/sec`);
        console.log(`ETA: ${stats.eta} seconds`);
      },
      (error) => {
        // Error callback
        console.error('Upload error:', error);
      },
      (chunkIndex, retryCount, maxRetries) => {
        // Retry callback
        console.log(`Retrying chunk ${chunkIndex} (${retryCount}/${maxRetries})`);
      }
    );
    
    console.log('Upload completed:', result.data.fileUrl);
  } catch (error) {
    console.error('Upload failed:', error);
  }
};
```

## Configuration

### Chunk Size Optimization

The system automatically optimizes chunk sizes based on:
- File size (2MB for <100MB, 5MB for <500MB, 10MB for >500MB)
- File type (larger chunks for videos)
- Connection speed (if available)

```javascript
// Example configuration
const config = {
  CHUNK_SIZE: 5 * 1024 * 1024, // 5MB default
  MAX_RETRIES: 3,
  CONCURRENCY_LIMIT: 2,
  TIMEOUT: 300000 // 5 minutes per chunk
};
```

### File Type Settings

```javascript
const FILE_TYPE_CONFIG = {
  VIDEO: {
    FORCE_CHUNKED: true,
    CHUNK_SIZE: 10 * 1024 * 1024, // 10MB
    TIMEOUT: 7200000, // 2 hours
  },
  DOCUMENT: {
    CHUNK_SIZE: 5 * 1024 * 1024, // 5MB
    TIMEOUT: 1800000, // 30 minutes
  }
};
```

## Error Handling

### Retry Logic
- **Chunk-level retries**: Each chunk is retried up to 3 times
- **Exponential backoff**: Delays increase with each retry (1s, 2s, 4s)
- **Upload-level retries**: Entire upload can be resumed after failure

### Error Types
1. **Network Errors**: Automatic retry with backoff
2. **Timeout Errors**: Retry with extended timeout
3. **Server Errors**: Limited retries with longer delays
4. **Client Errors**: No retry, user intervention required

## Performance Optimizations

### Memory Management
- Chunks are processed one at a time to avoid memory issues
- Temporary chunks are cleaned up immediately after assembly
- File streams are used instead of loading entire files into memory

### Network Optimization
- Concurrent chunk uploads (limited to 2 simultaneous)
- Connection pooling for HTTP requests
- Compression for metadata and small chunks

### Storage Optimization
- S3 multipart upload for cloud storage
- Temporary local storage for chunk assembly
- Automatic cleanup of failed uploads

## Testing

### Backend Testing
```bash
# Run chunked upload test
node Backend/scripts/test-chunked-upload.js
```

### Frontend Testing
The chunked upload functionality is automatically tested when uploading files >100MB in the Add Strategy page.

## Monitoring and Analytics

### Upload Metrics
- Upload success/failure rates
- Average upload speeds
- Chunk retry statistics
- Error frequency by type

### Performance Monitoring
- Memory usage during uploads
- Network utilization
- Server response times
- Storage I/O performance

## Troubleshooting

### Common Issues

1. **Chunks failing consistently**
   - Check network connectivity
   - Verify server timeout settings
   - Check available disk space

2. **Slow upload speeds**
   - Reduce chunk size
   - Increase concurrency limit
   - Check network bandwidth

3. **Memory issues**
   - Reduce chunk size
   - Limit concurrent uploads
   - Check available RAM

### Debug Mode
Enable debug logging by setting:
```javascript
localStorage.setItem('chunkedUploadDebug', 'true');
```

## Future Enhancements

1. **Adaptive Chunk Sizing**: Dynamic chunk size based on real-time network conditions
2. **Parallel Processing**: Multiple concurrent chunk uploads with intelligent queuing
3. **Compression**: On-the-fly compression for certain file types
4. **Bandwidth Throttling**: Respect user's bandwidth limitations
5. **Background Uploads**: Continue uploads even when page is not active
6. **Upload Scheduling**: Queue uploads for optimal network conditions

## Security Considerations

1. **Authentication**: All upload endpoints require valid JWT tokens
2. **File Validation**: Chunks are validated for size and type
3. **Rate Limiting**: Upload endpoints have rate limiting to prevent abuse
4. **Cleanup**: Temporary files are automatically cleaned up
5. **Access Control**: Users can only access their own upload sessions

## Browser Compatibility

- **Chrome**: Full support
- **Firefox**: Full support
- **Safari**: Full support
- **Edge**: Full support
- **IE11**: Limited support (fallback to standard upload)

## Dependencies

### Backend
- `multer`: File upload handling
- `aws-sdk`: S3 integration
- `fs`: File system operations

### Frontend
- `axios`: HTTP client
- `react`: UI framework
- File API: Browser file handling

## Conclusion

The chunked upload implementation provides a robust, scalable solution for handling large video files in the MERN Sports Hub application. It addresses the original timeout and reliability issues while providing an enhanced user experience with detailed progress indicators and error handling.
