import api from './api';
import {
  getUploadConfig,
  isChunkedUploadSupported,
  getOptimalChunkSize,
  CHUNKED_UPLOAD_CONFIG,
  ERROR_CONFIG
} from '../config/uploadConfig';

/**
 * Chunked Upload Service
 * Handles large file uploads by splitting them into smaller chunks
 */

class ChunkedUploadService {
  constructor() {
    this.activeUploads = new Map();
  }

  /**
   * Upload a file using chunked upload
   * @param {File} file - The file to upload
   * @param {string} contentType - Content type (Video, Document, etc.)
   * @param {Function} onProgress - Progress callback (progress, speed, eta)
   * @param {Function} onError - Error callback
   * @param {Function} onRetry - Retry callback
   * @returns {Promise} Upload result
   */
  async uploadFile(file, contentType, onProgress, onError, onRetry) {
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    try {
      // Check browser support
      if (!isChunkedUploadSupported()) {
        throw new Error('Chunked upload not supported in this browser');
      }

      // Get upload configuration
      const config = getUploadConfig(file, contentType);
      const chunkSize = getOptimalChunkSize(file.size);

      // Calculate chunks
      const totalChunks = Math.ceil(file.size / chunkSize);
      const chunks = this.createChunks(file, totalChunks, chunkSize);
      
      console.log(`[ChunkedUpload] Starting upload: ${file.name} (${totalChunks} chunks)`);
      
      // Initialize upload session
      const initResponse = await this.initializeUpload(file, totalChunks);
      const serverUploadId = initResponse.data.uploadId;
      
      // Track upload state
      const uploadState = {
        uploadId,
        serverUploadId,
        file,
        chunks,
        totalChunks,
        uploadedChunks: new Set(),
        failedChunks: new Map(),
        startTime: Date.now(),
        lastProgressTime: Date.now(),
        uploadedBytes: 0,
        onProgress,
        onError,
        onRetry
      };
      
      this.activeUploads.set(uploadId, uploadState);
      
      // Upload chunks
      const result = await this.uploadChunks(uploadState);
      
      // Complete upload
      const finalResult = await this.completeUpload(serverUploadId);
      
      // Cleanup
      this.activeUploads.delete(uploadId);
      
      console.log(`[ChunkedUpload] Upload completed: ${file.name}`);
      return finalResult;
      
    } catch (error) {
      console.error('[ChunkedUpload] Upload failed:', error);
      this.activeUploads.delete(uploadId);
      throw error;
    }
  }

  /**
   * Resume a failed upload
   * @param {string} uploadId - Upload ID to resume
   * @returns {Promise} Upload result
   */
  async resumeUpload(uploadId) {
    const uploadState = this.activeUploads.get(uploadId);
    if (!uploadState) {
      throw new Error('Upload session not found');
    }

    console.log(`[ChunkedUpload] Resuming upload: ${uploadState.file.name}`);
    
    try {
      // Get current status from server
      const statusResponse = await this.getUploadStatus(uploadState.serverUploadId);
      const serverProgress = statusResponse.data;
      
      // Update local state based on server state
      uploadState.uploadedChunks.clear();
      for (let i = 0; i < serverProgress.uploadedChunks; i++) {
        uploadState.uploadedChunks.add(i);
      }
      
      // Continue uploading remaining chunks
      const result = await this.uploadChunks(uploadState);
      
      // Complete upload
      const finalResult = await this.completeUpload(uploadState.serverUploadId);
      
      // Cleanup
      this.activeUploads.delete(uploadId);
      
      console.log(`[ChunkedUpload] Resume completed: ${uploadState.file.name}`);
      return finalResult;
      
    } catch (error) {
      console.error('[ChunkedUpload] Resume failed:', error);
      throw error;
    }
  }

  /**
   * Cancel an active upload
   * @param {string} uploadId - Upload ID to cancel
   */
  cancelUpload(uploadId) {
    const uploadState = this.activeUploads.get(uploadId);
    if (uploadState) {
      uploadState.cancelled = true;
      this.activeUploads.delete(uploadId);
      console.log(`[ChunkedUpload] Upload cancelled: ${uploadState.file.name}`);
    }
  }

  /**
   * Create file chunks
   * @param {File} file - File to chunk
   * @param {number} totalChunks - Total number of chunks
   * @param {number} chunkSize - Size of each chunk
   * @returns {Array} Array of chunk objects
   */
  createChunks(file, totalChunks, chunkSize) {
    const chunks = [];
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      chunks.push({
        index: i,
        data: chunk,
        size: chunk.size,
        start,
        end
      });
    }
    return chunks;
  }

  /**
   * Initialize upload session on server
   */
  async initializeUpload(file, totalChunks) {
    const response = await api.post('/content/upload/init', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      totalChunks
    });
    return response.data;
  }

  /**
   * Upload all chunks with retry logic
   */
  async uploadChunks(uploadState) {
    const { chunks, uploadedChunks } = uploadState;

    // Create upload promises for remaining chunks
    const uploadPromises = chunks
      .filter(chunk => !uploadedChunks.has(chunk.index))
      .map(chunk => this.uploadChunkWithRetry(uploadState, chunk));

    // Upload chunks with concurrency limit (2 concurrent uploads)
    const concurrencyLimit = 2;
    const results = [];

    for (let i = 0; i < uploadPromises.length; i += concurrencyLimit) {
      const batch = uploadPromises.slice(i, i + concurrencyLimit);
      const batchResults = await Promise.all(batch);
      results.push(...batchResults);

      // Update progress after each batch
      this.updateProgress(uploadState);

      // Check if upload was cancelled
      if (uploadState.cancelled) {
        throw new Error('Upload cancelled by user');
      }
    }

    return results;
  }

  /**
   * Upload a single chunk with retry logic
   */
  async uploadChunkWithRetry(uploadState, chunk) {
    const { serverUploadId, failedChunks } = uploadState;
    let retryCount = failedChunks.get(chunk.index) || 0;
    const maxRetries = 3; // Use constant instead of undefined MAX_RETRIES

    while (retryCount < maxRetries) {
      try {
        const formData = new FormData();
        formData.append('chunk', chunk.data);
        formData.append('uploadId', serverUploadId);
        formData.append('chunkIndex', chunk.index.toString());

        const response = await api.post('/content/upload/chunk', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 300000, // 5 minutes per chunk
        });

        // Mark chunk as uploaded
        uploadState.uploadedChunks.add(chunk.index);
        uploadState.uploadedBytes += chunk.size;
        failedChunks.delete(chunk.index);

        console.log(`[ChunkedUpload] Chunk ${chunk.index + 1}/${uploadState.totalChunks} uploaded`);
        return response.data;

      } catch (error) {
        retryCount++;
        failedChunks.set(chunk.index, retryCount);

        console.error(`[ChunkedUpload] Chunk ${chunk.index} failed (attempt ${retryCount}/${maxRetries}):`, error);

        if (retryCount >= maxRetries) {
          throw new Error(`Chunk ${chunk.index} failed after ${maxRetries} attempts: ${error.message}`);
        }

        // Exponential backoff delay
        const retryDelayBase = 1000; // 1 second base delay
        const delay = retryDelayBase * Math.pow(2, retryCount - 1);
        await new Promise(resolve => setTimeout(resolve, delay));

        // Notify about retry
        if (uploadState.onRetry) {
          uploadState.onRetry(chunk.index, retryCount, maxRetries);
        }
      }
    }
  }

  /**
   * Complete the upload on server
   */
  async completeUpload(serverUploadId) {
    const response = await api.post('/content/upload/complete', {
      uploadId: serverUploadId
    });
    return response.data;
  }

  /**
   * Get upload status from server
   */
  async getUploadStatus(serverUploadId) {
    const response = await api.get(`/content/upload/status/${serverUploadId}`);
    return response.data;
  }

  /**
   * Update progress and calculate upload speed/ETA
   */
  updateProgress(uploadState) {
    const { uploadedChunks, totalChunks, uploadedBytes, file, startTime, onProgress } = uploadState;
    
    const progress = Math.round((uploadedChunks.size / totalChunks) * 100);
    const currentTime = Date.now();
    const elapsedTime = currentTime - startTime;
    
    // Calculate upload speed (bytes per second)
    const speed = uploadedBytes / (elapsedTime / 1000);
    
    // Calculate ETA (estimated time remaining)
    const remainingBytes = file.size - uploadedBytes;
    const eta = speed > 0 ? remainingBytes / speed : 0;
    
    if (onProgress) {
      onProgress({
        progress,
        uploadedChunks: uploadedChunks.size,
        totalChunks,
        uploadedBytes,
        totalBytes: file.size,
        speed,
        eta,
        elapsedTime
      });
    }
  }

  /**
   * Format bytes to human readable string
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Format time to human readable string
   */
  formatTime(seconds) {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m ${Math.round(seconds % 60)}s`;
    return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`;
  }
}

// Export singleton instance
export default new ChunkedUploadService();
