import React, { useState, useEffect } from 'react';
import { FaExclamationTriangle, FaSync } from 'react-icons/fa';
import UniversalPDFViewer from './UniversalPDFViewer';
import { useSignedUrl } from '../../hooks/useSignedUrl';
import { isS3Url } from '../../utils/constants';
import '../../styles/DocumentViewer.css';

const DocumentViewer = ({
  fileUrl,
  fileName = '',
  title = 'Document',
  className = '',
  height = '400px',
  showDownload = false,
  onDownload = null
}) => {
  const [hasError, setHasError] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use the signed URL hook for automatic URL management
  const {
    signedUrl,
    isLoading: isUrlLoading,
    error: urlError,
    refresh: refreshUrl,
    needsRefresh
  } = useSignedUrl(fileUrl, {
    type: 'preview',
    autoRefresh: true,
    onError: (error) => {
      console.error('[DocumentViewer] URL refresh error:', error);
      setHasError(true);
    },
    onRefresh: (newUrl) => {
      console.log('[DocumentViewer] URL refreshed:', newUrl);
      setHasError(false);
    }
  });

  // Use the signed URL if available, otherwise fall back to original
  const effectiveUrl = signedUrl || fileUrl;

  // Handle manual refresh
  const handleManualRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshUrl();
    } catch (error) {
      console.error('[DocumentViewer] Manual refresh failed:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Initialize component - simplified since all documents are PDFs
  useEffect(() => {
    if (!fileUrl) {
      setHasError(true);
      return;
    }

    // Reset error state when URL changes
    setHasError(false);
  }, [fileUrl]);

  const handleDownload = () => {
    const downloadUrl = effectiveUrl || fileUrl;
    if (downloadUrl) {
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  // Show loading state while URL is being fetched
  const isLoading = isUrlLoading && !effectiveUrl;

  return (
    <div className={`document-viewer ${className}`} style={{ height }}>
      {isLoading ? (
        <div className="document-viewer__loading">
          <div className="document-viewer__spinner" />
          <p>Loading PDF document...</p>
        </div>
      ) : hasError || urlError ? (
        <div className="document-viewer__error">
          <FaExclamationTriangle className="document-viewer__error-icon" />
          <h3>Error Loading PDF Document</h3>
          <p>
            {urlError
              ? 'Unable to generate secure access URL. Please try refreshing.'
              : 'Unable to load the PDF preview. Please check the file URL.'
            }
          </p>
          <div className="document-viewer__error-actions">
            {needsRefresh && (
              <button
                className="document-viewer__refresh-btn"
                onClick={handleManualRefresh}
                disabled={isRefreshing}
              >
                <FaSync className={isRefreshing ? 'spinning' : ''} />
                {isRefreshing ? 'Refreshing...' : 'Refresh URL'}
              </button>
            )}
            {(showDownload || onDownload) && (
              <button
                className="document-viewer__download-btn"
                onClick={onDownload || handleDownload}
              >
                Download
              </button>
            )}
          </div>
        </div>
      ) : (
        <UniversalPDFViewer
          fileUrl={effectiveUrl}
          fileName={fileName}
          title={title}
          className={className}
          height={height}
          showDownload={showDownload}
          onDownload={onDownload}
          showNativeOptions={false}
          onError={() => {
            // If PDF viewer fails, try refreshing the URL
            if (isS3Url(fileUrl) && !isRefreshing) {
              console.log('[DocumentViewer] PDF load failed, attempting URL refresh');
              handleManualRefresh();
            } else {
              setHasError(true);
            }
          }}
        />
      )}
    </div>
  );
};

export default DocumentViewer;
