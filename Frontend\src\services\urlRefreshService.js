import { API_BASE_URL } from '../utils/constants';

/**
 * Frontend URL Refresh Service
 * Handles automatic refresh of signed URLs when they expire
 */
class URLRefreshService {
  constructor() {
    this.refreshPromises = new Map(); // Track ongoing refresh requests
    this.urlCache = new Map(); // Cache URL metadata
    this.retryAttempts = new Map(); // Track retry attempts
    this.maxRetries = 3;
    this.retryDelay = 1000; // 1 second base delay
  }

  /**
   * Get or refresh a signed URL
   * @param {string} filePath - Original file path or S3 URL
   * @param {string} type - URL type ('content' or 'preview')
   * @param {boolean} forceRefresh - Force refresh even if cached
   * @returns {Promise<string>} - Signed URL
   */
  async getSignedUrl(filePath, type = 'content', forceRefresh = false) {
    if (!filePath) return null;

    // Check if this is already a signed URL
    if (this.isSignedUrl(filePath)) {
      // If it's already signed and not expired, return it
      if (!forceRefresh && !this.isSignedUrlExpired(filePath)) {
        console.log('[URLRefresh] Using existing signed URL (not expired)');
        return filePath;
      }

      // If it's expired or force refresh, we need to extract the original key
      console.log('[URLRefresh] Signed URL expired or force refresh requested, will refresh');
    }

    // Extract S3 key from the file path
    const s3Key = this.extractS3Key(filePath);
    if (!s3Key) {
      console.log('[URLRefresh] Could not extract S3 key, returning original URL');
      // If we can't extract the key but it's a signed URL that's not expired, return it
      if (this.isSignedUrl(filePath) && !this.isSignedUrlExpired(filePath)) {
        return filePath;
      }
      return filePath;
    }

    console.log('[URLRefresh] Extracted S3 key:', s3Key);

    const cacheKey = `${s3Key}:${type}`;

    // Check if we have a valid cached URL
    if (!forceRefresh && this.urlCache.has(cacheKey)) {
      const cached = this.urlCache.get(cacheKey);
      const now = new Date();
      const timeUntilExpiry = new Date(cached.expiresAt) - now;

      // If URL expires in more than 5 minutes, return cached version
      if (timeUntilExpiry > 5 * 60 * 1000) {
        console.log('[URLRefresh] Using cached URL');
        return cached.url;
      }
    }

    // Check if refresh is already in progress
    if (this.refreshPromises.has(cacheKey)) {
      console.log('[URLRefresh] Refresh already in progress, waiting...');
      return await this.refreshPromises.get(cacheKey);
    }

    // Start refresh process
    console.log('[URLRefresh] Starting refresh process for key:', s3Key);
    const refreshPromise = this.performRefresh(s3Key, type, forceRefresh);
    this.refreshPromises.set(cacheKey, refreshPromise);

    try {
      const result = await refreshPromise;
      return result;
    } finally {
      this.refreshPromises.delete(cacheKey);
    }
  }

  /**
   * Perform the actual URL refresh
   * @param {string} s3Key - S3 object key
   * @param {string} type - URL type
   * @param {boolean} forceRefresh - Force refresh
   * @returns {Promise<string>} - Signed URL
   */
  async performRefresh(s3Key, type, forceRefresh) {
    const retryKey = `${s3Key}:${type}`;
    const currentRetries = this.retryAttempts.get(retryKey) || 0;

    try {
      const token = localStorage.getItem('xosportshub_token');
      if (!token) {
        console.warn('[URLRefresh] No authentication token available, cannot refresh URL');
        // Return a fallback URL or the original if we can't refresh
        return this.constructFallbackUrl(s3Key);
      }

      const endpoint = forceRefresh
        ? `${API_BASE_URL}/content/file/${encodeURIComponent(s3Key)}/refresh`
        : `${API_BASE_URL}/content/file/${encodeURIComponent(s3Key)}`;

      const method = forceRefresh ? 'POST' : 'GET';
      const body = forceRefresh ? JSON.stringify({ type }) : undefined;

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        ...(body && { body })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success || !data.data.signedUrl) {
        throw new Error('Invalid response from server');
      }

      // Cache the new URL
      const cacheKey = `${s3Key}:${type}`;
      this.urlCache.set(cacheKey, {
        url: data.data.signedUrl,
        expiresAt: data.data.expiresAt,
        s3Key: s3Key,
        type: type,
        refreshedAt: new Date().toISOString()
      });

      // Reset retry count on success
      this.retryAttempts.delete(retryKey);

      console.log(`[URLRefresh] Successfully refreshed URL for ${s3Key} (${type})`);
      return data.data.signedUrl;

    } catch (error) {
      console.error(`[URLRefresh] Error refreshing URL for ${s3Key}:`, error);

      // Implement retry logic
      if (currentRetries < this.maxRetries) {
        this.retryAttempts.set(retryKey, currentRetries + 1);

        // Exponential backoff
        const delay = this.retryDelay * Math.pow(2, currentRetries);
        console.log(`[URLRefresh] Retrying in ${delay}ms (attempt ${currentRetries + 1}/${this.maxRetries})`);

        await new Promise(resolve => setTimeout(resolve, delay));
        return this.performRefresh(s3Key, type, true); // Force refresh on retry
      }

      // Max retries exceeded, return original URL or cached if available
      const cacheKey = `${s3Key}:${type}`;
      const cached = this.urlCache.get(cacheKey);

      if (cached) {
        console.warn(`[URLRefresh] Using cached URL after ${this.maxRetries} failed attempts`);
        return cached.url;
      }

      // Fallback to backend endpoint URL
      const fallbackUrl = this.constructFallbackUrl(s3Key);
      console.warn(`[URLRefresh] Using fallback URL: ${fallbackUrl}`);
      return fallbackUrl;
    }
  }

  /**
   * Refresh multiple URLs in batch
   * @param {Array<string>} filePaths - Array of file paths
   * @param {string} type - URL type
   * @returns {Promise<Object>} - Map of original path to signed URL
   */
  async refreshMultipleUrls(filePaths, type = 'content') {
    const s3Keys = filePaths
      .map(path => this.extractS3Key(path))
      .filter(key => key !== null);

    if (s3Keys.length === 0) {
      return {};
    }

    try {
      const token = localStorage.getItem('xosportshub_token');
      if (!token) {
        throw new Error('No authentication token available');
      }

      const response = await fetch(`${API_BASE_URL}/content/files/refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          fileKeys: s3Keys,
          type: type
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error('Batch refresh failed');
      }

      // Update cache with results
      const results = {};
      for (const [s3Key, urlData] of Object.entries(data.data.results)) {
        if (urlData.url) {
          const cacheKey = `${s3Key}:${type}`;
          this.urlCache.set(cacheKey, {
            url: urlData.url,
            expiresAt: urlData.expiresAt,
            s3Key: s3Key,
            type: type,
            refreshedAt: new Date().toISOString()
          });

          // Map back to original file path
          const originalPath = filePaths.find(path => this.extractS3Key(path) === s3Key);
          if (originalPath) {
            results[originalPath] = urlData.url;
          }
        }
      }

      console.log(`[URLRefresh] Batch refreshed ${Object.keys(results).length} URLs`);
      return results;

    } catch (error) {
      console.error('[URLRefresh] Batch refresh failed:', error);
      return {};
    }
  }

  /**
   * Check if a URL needs refresh
   * @param {string} filePath - File path
   * @param {string} type - URL type
   * @returns {boolean} - Whether refresh is needed
   */
  needsRefresh(filePath, type = 'content') {
    if (!filePath) return false;

    // If it's already a signed URL, check if it's expired
    if (this.isSignedUrl(filePath)) {
      return this.isSignedUrlExpired(filePath);
    }

    // Otherwise, check our cache
    const s3Key = this.extractS3Key(filePath);
    if (!s3Key) return false;

    const cacheKey = `${s3Key}:${type}`;
    const cached = this.urlCache.get(cacheKey);

    if (!cached) return true;

    const now = new Date();
    const timeUntilExpiry = new Date(cached.expiresAt) - now;

    // Refresh if expires in less than 10 minutes
    return timeUntilExpiry < 10 * 60 * 1000;
  }

  /**
   * Check if a URL is already a signed URL
   * @param {string} url - URL to check
   * @returns {boolean} - Whether the URL is signed
   */
  isSignedUrl(url) {
    return url && url.includes('X-Amz-Algorithm=AWS4-HMAC-SHA256');
  }

  /**
   * Check if a signed URL is expired or will expire soon
   * @param {string} url - Signed URL to check
   * @returns {boolean} - Whether the URL is expired or will expire soon
   */
  isSignedUrlExpired(url) {
    if (!this.isSignedUrl(url)) return false;

    try {
      const urlObj = new URL(url);
      const expiresParam = urlObj.searchParams.get('X-Amz-Expires');
      const dateParam = urlObj.searchParams.get('X-Amz-Date');

      if (!expiresParam || !dateParam) return true;

      // Parse the date from X-Amz-Date (format: 20250717T070950Z)
      const year = parseInt(dateParam.substring(0, 4));
      const month = parseInt(dateParam.substring(4, 6)) - 1; // Month is 0-indexed
      const day = parseInt(dateParam.substring(6, 8));
      const hour = parseInt(dateParam.substring(9, 11));
      const minute = parseInt(dateParam.substring(11, 13));
      const second = parseInt(dateParam.substring(13, 15));

      const signedDate = new Date(year, month, day, hour, minute, second);
      const expiresIn = parseInt(expiresParam);
      const expiryDate = new Date(signedDate.getTime() + (expiresIn * 1000));

      const now = new Date();
      const timeUntilExpiry = expiryDate - now;

      // Consider expired if expires in less than 10 minutes
      return timeUntilExpiry < 10 * 60 * 1000;
    } catch (error) {
      console.error('[URLRefresh] Error checking URL expiry:', error);
      return true; // Assume expired if we can't parse
    }
  }

  /**
   * Extract S3 key from various URL formats
   * @param {string} url - URL to extract key from
   * @returns {string|null} - S3 key or null
   */
  extractS3Key(url) {
    if (!url || typeof url !== 'string') return null;

    // First, check if this is already a signed URL and extract the original path
    if (url.includes('X-Amz-Algorithm=AWS4-HMAC-SHA256')) {
      // This is already a signed URL, extract the path before the query string
      const urlObj = new URL(url);
      let path = urlObj.pathname.startsWith('/') ? urlObj.pathname.substring(1) : urlObj.pathname;

      // If the path starts with 'file/', remove it as it's our API endpoint prefix
      if (path.startsWith('file/')) {
        path = path.substring(5); // Remove 'file/' prefix
      }

      // The remaining path should be the S3 key
      return path;
    }

    // Handle different S3 URL formats
    const patterns = [
      // Standard S3 URL: https://bucket.s3.region.amazonaws.com/key
      /https:\/\/[^.]+\.s3\.[^.]+\.amazonaws\.com\/(.+)/,
      // Path-style S3 URL: https://s3.region.amazonaws.com/bucket/key
      /https:\/\/s3\.[^.]+\.amazonaws\.com\/[^/]+\/(.+)/,
      // CloudFront or custom domain: extract from path
      /\/uploads\/(.+)/,
      // Direct key (already extracted)
      /^uploads\/(.+)/,
      // File path format (remove file/ prefix)
      /^file\/(.+)/,
      /\/file\/(.+)/
    ];

    for (const pattern of patterns) {
      const match = url.match(pattern);
      if (match) {
        return match[1];
      }
    }

    // If it looks like it might be a direct S3 key
    if (url.includes('uploads/')) {
      const keyStart = url.indexOf('uploads/');
      return url.substring(keyStart);
    }

    // If it's a direct file path, remove the file/ prefix
    if (url.includes('file/')) {
      const keyStart = url.indexOf('file/');
      const fullPath = url.substring(keyStart);
      return fullPath.startsWith('file/') ? fullPath.substring(5) : fullPath;
    }

    return null;
  }

  /**
   * Construct fallback S3 URL
   * @param {string} s3Key - S3 key
   * @returns {string} - Fallback URL
   */
  constructS3Url(s3Key) {
    // This is a fallback - in production you might want to use your CDN domain
    return `https://your-bucket.s3.amazonaws.com/${s3Key}`;
  }

  /**
   * Construct fallback URL when refresh fails
   * @param {string} s3Key - S3 key
   * @returns {string} - Fallback URL
   */
  constructFallbackUrl(s3Key) {
    // Return a URL that points to our backend endpoint
    // This will let the backend handle the signed URL generation
    return `/api/content/file/${encodeURIComponent(s3Key)}`;
  }

  /**
   * Clear the URL cache
   */
  clearCache() {
    this.urlCache.clear();
    this.refreshPromises.clear();
    this.retryAttempts.clear();
    console.log('[URLRefresh] Cache cleared');
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache stats
   */
  getCacheStats() {
    return {
      cachedUrls: this.urlCache.size,
      activeRefreshes: this.refreshPromises.size,
      retryingUrls: this.retryAttempts.size
    };
  }
}

// Create singleton instance
const urlRefreshService = new URLRefreshService();

export default urlRefreshService;
