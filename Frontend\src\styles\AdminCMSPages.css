/* AdminCMSPages Component Styles */
.AdminCMSPages {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header */
.AdminCMSPages .AdminCMSPages__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--heading6);
 
}

.AdminCMSPages .header-left {
  flex: 1;
  max-width: 400px;
}

.AdminCMSPages .search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.AdminCMSPages .search-icon {
  position: absolute;
  left: var(--smallfont);
  color: var(--dark-gray);
  font-size: var(--basefont);
  z-index: 1;
}

.AdminCMSPages .search-input {
  width: 100%;
  padding: 16px var(--smallfont) 16px 40px;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: 12px;
  background-color: var(--white);
  transition: all 0.3s ease;
}

.AdminCMSPages .search-input:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.AdminCMSPages .search-input::placeholder {
  color: var(--dark-gray);
}

.AdminCMSPages .header-right {
  display: flex;
  gap: var(--smallfont);
}

.AdminCMSPages .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.AdminCMSPages .btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.AdminCMSPages .btn.btn-primary:hover {
  background-color: var(--btn-color);
}

.AdminCMSPages .btn.btn-outline {
  background-color: var(--btn-color);
  color: var(--white);
  border: 1px solid var(--light-gray);
}

.AdminCMSPages .btn.btn-outline:hover {
  background-color: var(--bg-gray);
  color: var(--white);
  
}

.AdminCMSPages .btn.btn-success {
  background-color: #10b981;
  color: var(--white);
}

.AdminCMSPages .btn.btn-success:hover {
  background-color: #059669;
}

.AdminCMSPages .btn.btn-danger {
  background-color: var(--btn-color);
  color: var(--white);
}

.AdminCMSPages .btn.btn-danger:hover {
  background-color: var(--btn-color);
}

/* Filters */
.AdminCMSPages .AdminCMSPages__filters {
  display: flex;
  align-items: center;
  gap: var(--basefont);


  border-radius: var(--border-radius);

}

.AdminCMSPages .filter-group {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AdminCMSPages .filter-select {
  padding:var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  cursor: pointer;
}

.AdminCMSPages .filter-select:focus {
  outline: none;
  border-color: var(--btn-color);
}

.AdminCMSPages .bulk-actions {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-left: auto;
}

.AdminCMSPages .selected-count {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

/* Table */
.AdminCMSPages .AdminCMSPages__table {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  overflow: hidden;

}

.AdminCMSPages .table-container {
  overflow-x: auto;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.AdminCMSPages .pages-table {
  width: 100%;
  border-collapse: collapse;
}

.AdminCMSPages .pages-table th,
.AdminCMSPages .pages-table td {
  padding: var(--basefont);
  text-align: left;
  border-bottom: 1px solid var(--bg-gray);
}

.AdminCMSPages .pages-table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.AdminCMSPages .pages-table td {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.AdminCMSPages .pages-table tr:hover {
  background-color: var(--bg-gray);
}

.AdminCMSPages .page-info {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AdminCMSPages .page-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--basefont);
}

.AdminCMSPages .page-details {
  display: flex;
  flex-direction: column;
}

.AdminCMSPages .page-title {
  font-weight: 600;
  color: var(--secondary-color);
}

.AdminCMSPages .page-slug {
  font-family: "Courier New", monospace;
  background-color: var(--bg-gray);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.AdminCMSPages .status-toggle {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AdminCMSPages .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.AdminCMSPages .status-badge.published {
  background-color: #dcfce7;
  color: #166534;
}

.AdminCMSPages .status-badge.draft {
  background-color: #f3f4f6;
  color: #374151;
}

.AdminCMSPages .toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: var(--heading6);
  transition: all 0.3s ease;
}

.AdminCMSPages .toggle-on {
  color: #10b981;
}

.AdminCMSPages .toggle-off {
  color: var(--light-gray);
}

.AdminCMSPages .toggle-btn:hover .toggle-on {
  color: #059669;
}

.AdminCMSPages .toggle-btn:hover .toggle-off {
  color: var(--dark-gray);
}

.AdminCMSPages .table-actions {
  display: flex;
  gap: 4px;
}

.AdminCMSPages .btn-action {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.AdminCMSPages .btn-action.edit {
  background-color: var(--bg-gray);
  color: var(--secondary-color);
}

.AdminCMSPages .btn-action.edit:hover {
  background-color: var(--light-gray);
  transform: scale(1.05);
}

.AdminCMSPages .btn-action.delete {
  background-color: #fef2f2;
  color: #ef4444;
}

.AdminCMSPages .btn-action.delete:hover {
  background-color: #fee2e2;
  transform: scale(1.05);
}

/* Tooltip for action buttons */
.AdminCMSPages .btn-action::before {
  content: attr(title);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--secondary-color);
  color: var(--white);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: var(--z-index-tooltip);
  pointer-events: none;
}

.AdminCMSPages .btn-action:hover::before {
  opacity: 1;
  visibility: visible;
  bottom: calc(100% + 8px);
}

/* Quick Actions */
.AdminCMSPages .AdminCMSPages__quick-actions {
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-light);
  padding: var(--heading6);
  margin-bottom: var(--heading6);
}

.AdminCMSPages .AdminCMSPages__quick-actions h3 {
  margin: 0 0 var(--basefont) 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.AdminCMSPages .quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--basefont);
}

.AdminCMSPages .quick-action-card {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  transition: all 0.3s ease;
}

.AdminCMSPages .quick-action-card:hover {
  border-color: var(--btn-color);
  box-shadow: var(--box-shadow-light);
}

.AdminCMSPages .action-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  color: var(--btn-color);
  font-size: var(--heading6);
}

.AdminCMSPages .action-content {
  flex: 1;
}

.AdminCMSPages .action-content h4 {
  margin: 0 0 4px 0;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.AdminCMSPages .action-content p {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* No Results */
.AdminCMSPages .no-results {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading3);
  text-align: center;
}

.AdminCMSPages .no-results-icon {
  font-size: var(--heading2);
  color: var(--light-gray);
  margin-bottom: var(--basefont);
}

.AdminCMSPages .no-results h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--secondary-color);
}

.AdminCMSPages .no-results p {
  margin: 0;
  color: var(--dark-gray);
  font-size: var(--smallfont);
}

/* Pagination */
.AdminCMSPages .AdminCMSPages__pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--basefont);
  background-color: var(--white);
  border-radius: var(--border-radius);
 
}

.AdminCMSPages .pagination-info {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.AdminCMSPages .pagination-controls {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.AdminCMSPages .page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.AdminCMSPages .page-number.active {
  background-color: var(--btn-color);
  color: var(--white);
}
.AdminCMSPages .status-controls{
  display: flex
;
    align-items: center;
    gap: 20px;
}

/* Responsive styles */
@media (max-width: 768px) {
  .AdminCMSPages .AdminCMSPages__header {
    flex-direction: column;
    align-items: stretch;
  }

  .AdminCMSPages .header-left {
    max-width: none;
  }

  .AdminCMSPages .AdminCMSPages__filters {
    flex-wrap: wrap;
  }

  .AdminCMSPages .bulk-actions {
    margin-left: 0;
    margin-top: var(--smallfont);
    width: 100%;
  }

  .AdminCMSPages .pages-table {
    font-size: var(--extrasmallfont);
  }

  .AdminCMSPages .pages-table th,
  .AdminCMSPages .pages-table td {
    padding: var(--smallfont);
  }

  .AdminCMSPages .page-icon {
    width: 32px;
    height: 32px;
    font-size: var(--smallfont);
  }

  .AdminCMSPages .quick-actions-grid {
    grid-template-columns: 1fr;
  }

  .AdminCMSPages .AdminCMSPages__pagination {
    flex-direction: column;
    gap: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .AdminCMSPages .table-actions {
    flex-direction: column;
  }

  .AdminCMSPages .btn-action {
    width: 28px;
    height: 28px;
    font-size: var(--extrasmallfont);
  }

  .AdminCMSPages .quick-action-card {
    flex-direction: column;
    text-align: center;
  }
}
