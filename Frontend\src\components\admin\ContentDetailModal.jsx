import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentContentDetail,
  selectLoading,
  hideContentDetailModal,
  setContentLoading,
  addActivity,
  selectUI,
} from "../../redux/slices/adminDashboardSlice";
import {
  updateContent,
  deleteContent,
  approveContent,
  rejectContent,
  updateContentStatus,
} from "../../redux/slices/adminDashboardThunks";
import "../../styles/ContentDetailModal.css";
import DocumentViewer from "../common/DocumentViewer";

// Icons
import {
  FaTimes,
  FaVideo,
  FaUser,
  FaCalendarAlt,
  FaDollarSign,
  FaEdit,
  FaTrash,
  FaCheck,
  FaEye,
  FaDownload,
  FaFlag,
  FaGavel,
  FaClock,
  FaToggleOn,
  FaToggleOff,
  FaHistory,
  FaShoppingCart,
  FaStar,
  <PERSON>a<PERSON>ileAlt,
  <PERSON>a<PERSON><PERSON>ua<PERSON>,
  <PERSON>a<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>halkboardTeacher
} from "react-icons/fa";
import { MdCategory, MdDescription, MdHistory } from "react-icons/md";
import { getSmartFileUrl, IMAGE_BASE_URL } from "../../utils/constants";

const ContentDetailModal = () => {
  const dispatch = useDispatch();
  const currentContent = useSelector(selectCurrentContentDetail);
  const loading = useSelector(selectLoading);
  const ui = useSelector(selectUI);
  const [isEditing, setIsEditing] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [editForm, setEditForm] = useState({});
  const [isPlaying, setIsPlaying] = useState(false);

  if (!currentContent || !ui.showContentDetailModal) return null;

  // Check if we're in view-only mode
  const isViewOnly = currentContent.viewOnly;

  // Determine if content is video
  const isVideoContent = () => {
    if (!currentContent.fileUrl) return false;
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
    const extension = currentContent.fileUrl.split('.').pop()?.toLowerCase();
    return videoExtensions.includes(extension);
  };

  // Determine content type
  const getContentType = () => {
    if (!currentContent.fileUrl) return 'unknown';

    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
    const extension = currentContent.fileUrl.split('.').pop()?.toLowerCase();

    if (videoExtensions.includes(extension)) return 'video';
    return 'document';
  };

  const formatDate = (date) => {
    return new Date(date).toLocaleString();
  };

  const getAuctionStatus = () => {
    if (!currentContent.isAuction) return null;
    return currentContent.auctionStatus || 'Unknown';
  };

  const handleStatusToggle = async () => {
    try {
      const newStatus = currentContent.status === 'Published' ? 'Draft' : 'Published';
      await dispatch(updateContentStatus({
        id: currentContent.id,
        status: newStatus,
        reason: `Status changed to ${newStatus} by admin`
      })).unwrap();

      dispatch(addActivity({
        id: Date.now(),
        type: 'content_status_update',
        description: `Content status updated: ${currentContent.title} - ${newStatus}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));
    } catch (error) {
      console.error('Failed to update content status:', error);
      alert('Failed to update content status. Please try again.');
    }
  };

  const handleClose = () => {
    dispatch(hideContentDetailModal());
    setIsEditing(false);
    setEditForm({});
  };

  const handleEdit = () => {
    if (isViewOnly) return; // Prevent editing in view-only mode
    setIsEditing(true);
    setEditForm({
      title: currentContent?.title || '',
      description: currentContent?.description || '',
      category: currentContent?.category || '',
      price: currentContent?.price || 0,
      status: currentContent?.status || '',
    });
  };

  const handleSave = async () => {
    try {
      dispatch(setContentLoading(true));

      // Dispatch the async thunk
      await dispatch(updateContent({ id: currentContent.id, contentData: editForm })).unwrap();

      dispatch(addActivity({
        id: Date.now(),
        type: 'content_update',
        description: `Content updated: ${editForm.title}`,
        timestamp: new Date().toISOString(),
        user: 'Admin',
      }));

      setIsEditing(false);

      // Show success message
      alert(`Content "${editForm.title}" has been updated successfully!`);
    } catch (error) {
      console.error('Failed to update content:', error);
      alert('Failed to update content. Please try again.');
    } finally {
      dispatch(setContentLoading(false));
    }
  };

  const handleApprove = async () => {
    if (window.confirm(`Are you sure you want to approve "${currentContent.title}"?`)) {
      try {
        await dispatch(approveContent({
          id: currentContent.id,
          approvalNotes: 'Approved from detail modal'
        })).unwrap();
        dispatch(addActivity({
          id: Date.now(),
          type: 'content_approval',
          description: `Content approved: ${currentContent.title}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));
        alert(`Content "${currentContent.title}" has been approved successfully!`);
      } catch (error) {
        console.error('Failed to approve content:', error);
        alert('Failed to approve content. Please try again.');
      }
    }
  };

  const handleReject = async () => {
    const reason = prompt(`Please provide a reason for rejecting "${currentContent.title}":`);
    if (reason) {
      try {
        await dispatch(rejectContent({
          id: currentContent.id,
          reason,
          rejectionNotes: reason
        })).unwrap();
        dispatch(addActivity({
          id: Date.now(),
          type: 'content_rejection',
          description: `Content rejected: ${currentContent.title} - Reason: ${reason}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));
        alert(`Content "${currentContent.title}" has been rejected.`);
      } catch (error) {
        console.error('Failed to reject content:', error);
        alert('Failed to reject content. Please try again.');
      }
    }
  };

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete "${currentContent.title}"? This action cannot be undone.`)) {
      try {
        await dispatch(deleteContent(currentContent.id)).unwrap();
        dispatch(addActivity({
          id: Date.now(),
          type: 'content_deletion',
          description: `Content deleted: ${currentContent.title}`,
          timestamp: new Date().toISOString(),
          user: 'Admin',
        }));
        handleClose();
        alert(`Content "${currentContent.title}" has been deleted successfully!`);
      } catch (error) {
        console.error('Failed to delete content:', error);
        alert('Failed to delete content. Please try again.');
      }
    }
  };

  const getStatusBadge = (status) => {
    const statusConfig = {
      Published: { class: 'status-approved', label: 'Published' },
      Draft: { class: 'status-draft', label: 'Draft' },
      pending: { class: 'status-pending', label: 'Pending Review' },
      rejected: { class: 'status-rejected', label: 'Rejected' },
    };

    const config = statusConfig[status] || statusConfig.Draft;
    return <span className={`status-badge ${config.class}`}>{config.label}</span>;
  };

  return (
    <div className="ContentDetailModal">
      <div className="ContentDetailModal__overlay" onClick={handleClose} />
      <div className="ContentDetailModal__container">
        {/* Header */}
        <div className="ContentDetailModal__header">
          <div className="header-content">
            <div className="content-thumbnail">
              {currentContent?.thumbnailUrl ? (
                <img src={IMAGE_BASE_URL + currentContent.thumbnailUrl} alt={currentContent?.title || 'Content'} />
              ) : (
                <FaVideo />
              )}
            </div>
            <div className="content-basic-info">
              <h2>{currentContent?.title || 'Untitled Content'}</h2>
              <div className="content-badges">
                {getStatusBadge(currentContent?.status)}
                <span className="category-badge">{currentContent?.category || 'Uncategorized'}</span>
                <span className="type-badge">{currentContent?.contentType || 'Unknown Type'}</span>
              </div>
            </div>
          </div>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content Preview */}
        <div className="ContentDetailModal__preview-section">
          {currentContent.fileUrl && (
            <>
              {isVideoContent() ? (
                <div className="ContentDetailModal__video-container">
                  <video
                    className="ContentDetailModal__video"
                    controls
                    autoPlay={false}
                    controlsList="nodownload noremoteplayback"
                    disablePictureInPicture
                    onPlay={() => setIsPlaying(true)}
                    onPause={() => setIsPlaying(false)}
                    style={{ width: '100%', maxHeight: '500px' }}
                  >
                    <source src={getSmartFileUrl(currentContent.fileUrl)} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
              ) : (
                <div className="ContentDetailModal__document-container">
                  <DocumentViewer
                    fileUrl={getSmartFileUrl(currentContent.fileUrl)}
                    fileName={currentContent.fileUrl.split('/').pop()}
                    title={currentContent.title}
                    height="100%"
                    showDownload={false}
                  />
                </div>
              )}
            </>
          )}
        </div>

        {/* Content */}
        <div className="ContentDetailModal__content">
          {/* Basic Information */}
          <div className="info-section">
            <h3>Basic Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <MdDescription className="info-icon" />
                <div>
                  <span className="info-label">Description</span>
                  <span className="info-value" dangerouslySetInnerHTML={{ __html: currentContent.description }} />
                </div>
              </div>
              <div className="info-item">
                <FaChalkboardTeacher className="info-icon" />
                <div>
                  <span className="info-label">Coach Name</span>
                  <span className="info-value">{currentContent.coachName}</span>
                </div>
              </div>
              <div className="info-item">
                <MdCategory className="info-icon" />
                <div>
                  <span className="info-label">Sport</span>
                  <span className="info-value">{currentContent.sport}</span>
                </div>
              </div>
              <div className="info-item">
                <FaDumbbell className="info-icon" />
                <div>
                  <span className="info-label">Difficulty</span>
                  <span className="info-value">{currentContent.difficulty}</span>
                </div>
              </div>
              <div className="info-item">
                <FaLanguage className="info-icon" />
                <div>
                  <span className="info-label">Language</span>
                  <span className="info-value">{currentContent.language}</span>
                </div>
              </div>
              <div className="info-item">
                <FaFileAlt className="info-icon" />
                <div>
                  <span className="info-label">Content Type</span>
                  <span className="info-value">{currentContent.contentType}</span>
                </div>
              </div>
            </div>
          </div>

          {/* About Coach & Strategic Content */}
          {(currentContent.aboutCoach || currentContent.strategicContent) && (
            <div className="info-section">
              <h3>Coach Information</h3>
              <div className="info-grid">
                {currentContent.aboutCoach && (
                  <div className="info-item full-width">
                    <FaChalkboardTeacher className="info-icon" />
                    <div>
                      <span className="info-label">About Coach</span>
                      <span className="info-value" dangerouslySetInnerHTML={{ __html: currentContent.aboutCoach }} />
                    </div>
                  </div>
                )}
                {currentContent.strategicContent && (
                  <div className="info-item full-width">
                    <FaFileAlt className="info-icon" />
                    <div>
                      <span className="info-label">Strategic Content</span>
                      <span className="info-value" dangerouslySetInnerHTML={{ __html: currentContent.strategicContent }} />
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* File Information */}
          <div className="info-section">
            <h3>File Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <FaFileAlt className="info-icon" />
                <div>
                  <span className="info-label">File Size</span>
                  <span className="info-value">{currentContent.fileSize ? `${(currentContent.fileSize / 1024 / 1024).toFixed(2)} MB` : 'N/A'}</span>
                </div>
              </div>
              <div className="info-item">
                <FaFlag className="info-icon" />
                <div>
                  <span className="info-label">Preview Status</span>
                  <span className="info-value">{currentContent.previewStatus || 'N/A'}</span>
                </div>
              </div>
              {currentContent.duration && (
                <div className="info-item">
                  <FaClock className="info-icon" />
                  <div>
                    <span className="info-label">Duration</span>
                    <span className="info-value">{currentContent.duration}</span>
                  </div>
                </div>
              )}
              {currentContent.videoLength && (
                <div className="info-item">
                  <FaClock className="info-icon" />
                  <div>
                    <span className="info-label">Video Length</span>
                    <span className="info-value">{currentContent.videoLength}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Seller Information */}
          <div className="info-section">
            <h3>Seller Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <FaUser className="info-icon" />
                <div>
                  <span className="info-label">Seller Name</span>
                  <span className="info-value">{currentContent.seller?.fullName || `${currentContent.seller?.firstName} ${currentContent.seller?.lastName}`.trim() || 'Unknown'}</span>
                </div>
              </div>
              <div className="info-item">
                <FaUser className="info-icon" />
                <div>
                  <span className="info-label">Seller Email</span>
                  <span className="info-value">{currentContent.seller?.email || 'N/A'}</span>
                </div>
              </div>
              <div className="info-item">
                <FaUser className="info-icon" />
                <div>
                  <span className="info-label">Seller ID</span>
                  <span className="info-value">{currentContent.seller?.id || currentContent.seller?._id || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Pricing & Sale Information */}
          <div className="info-section">
            <h3>Pricing & Sale Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <FaDollarSign className="info-icon" />
                <div>
                  <span className="info-label">Price</span>
                  <span className="info-value">${currentContent.price || 0}</span>
                </div>
              </div>
              <div className="info-item">
                <FaGavel className="info-icon" />
                <div>
                  <span className="info-label">Sale Type</span>
                  <span className="info-value">{currentContent.saleType}</span>
                </div>
              </div>
              <div className="info-item">
                <FaFlag className="info-icon" />
                <div>
                  <span className="info-label">Sold Status</span>
                  <span className="info-value">{currentContent.isSold ? 'Yes' : 'No'}</span>
                </div>
              </div>
              <div className="info-item">
                <FaFlag className="info-icon" />
                <div>
                  <span className="info-label">Custom Requests</span>
                  <span className="info-value">{currentContent.allowCustomRequests ? 'Allowed' : 'Not Allowed'}</span>
                </div>
              </div>
              {currentContent.customRequestPrice && (
                <div className="info-item">
                  <FaDollarSign className="info-icon" />
                  <div>
                    <span className="info-label">Custom Request Price</span>
                    <span className="info-value">${currentContent.customRequestPrice}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Tags and Metadata */}
          {(currentContent.tags?.length > 0 || currentContent.prerequisites?.length > 0 || currentContent.learningObjectives?.length > 0 || currentContent.equipment?.length > 0) && (
            <div className="info-section">
              <h3>Metadata</h3>
              <div className="info-grid">
                {currentContent.tags?.length > 0 && (
                  <div className="info-item full-width">
                    <FaFlag className="info-icon" />
                    <div>
                      <span className="info-label">Tags</span>
                      <span className="info-value">{currentContent.tags.join(', ')}</span>
                    </div>
                  </div>
                )}
                {currentContent.prerequisites?.length > 0 && (
                  <div className="info-item full-width">
                    <FaFileAlt className="info-icon" />
                    <div>
                      <span className="info-label">Prerequisites</span>
                      <span className="info-value">{currentContent.prerequisites.join(', ')}</span>
                    </div>
                  </div>
                )}
                {currentContent.learningObjectives?.length > 0 && (
                  <div className="info-item full-width">
                    <FaFileAlt className="info-icon" />
                    <div>
                      <span className="info-label">Learning Objectives</span>
                      <span className="info-value">{currentContent.learningObjectives.join(', ')}</span>
                    </div>
                  </div>
                )}
                {currentContent.equipment?.length > 0 && (
                  <div className="info-item full-width">
                    <FaDumbbell className="info-icon" />
                    <div>
                      <span className="info-label">Equipment</span>
                      <span className="info-value">{currentContent.equipment.join(', ')}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Important Dates */}
          <div className="info-section">
            <h3>Important Dates</h3>
            <div className="info-grid">
              <div className="info-item">
                <FaCalendarAlt className="info-icon" />
                <div>
                  <span className="info-label">Created Date</span>
                  <span className="info-value">{formatDate(currentContent.createdAt)}</span>
                </div>
              </div>
              <div className="info-item">
                <FaCalendarAlt className="info-icon" />
                <div>
                  <span className="info-label">Upload Date</span>
                  <span className="info-value">{formatDate(currentContent.uploadDate)}</span>
                </div>
              </div>
              {currentContent.publishedDate && (
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Published Date</span>
                    <span className="info-value">{formatDate(currentContent.publishedDate)}</span>
                  </div>
                </div>
              )}
              {currentContent.lastUpdated && (
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Last Updated</span>
                    <span className="info-value">{formatDate(currentContent.lastUpdated)}</span>
                  </div>
                </div>
              )}
              {currentContent.soldAt && (
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Sold Date</span>
                    <span className="info-value">{formatDate(currentContent.soldAt)}</span>
                  </div>
                </div>
              )}
              {currentContent.auctionEndedAt && (
                <div className="info-item">
                  <FaCalendarAlt className="info-icon" />
                  <div>
                    <span className="info-label">Auction Ended</span>
                    <span className="info-value">{formatDate(currentContent.auctionEndedAt)}</span>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Auction Winning Details */}
          {(currentContent.winningBidId || currentContent.winningOfferId) && (
            <div className="info-section">
              <h3>Auction Results</h3>
              <div className="info-grid">
                {currentContent.winningBidId && (
                  <div className="info-item">
                    <FaGavel className="info-icon" />
                    <div>
                      <span className="info-label">Winning Bid ID</span>
                      <span className="info-value">{currentContent.winningBidId}</span>
                    </div>
                  </div>
                )}
                {currentContent.winningOfferId && (
                  <div className="info-item">
                    <FaGavel className="info-icon" />
                    <div>
                      <span className="info-label">Winning Offer ID</span>
                      <span className="info-value">{currentContent.winningOfferId}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Auction Details */}
          {currentContent.saleType === 'Auction' && (
            <div className="info-section">
              <h3>Auction Details</h3>
              <div className="info-grid">
                <div className="info-item">
                  <FaGavel className="info-icon" />
                  <div>
                    <span className="info-label">Auction Status</span>
                    <span className={`info-value status-${currentContent.auctionStatus?.toLowerCase()}`}>
                      {currentContent.auctionStatus}
                    </span>
                  </div>
                </div>
                <div className="info-item">
                  <FaDollarSign className="info-icon" />
                  <div>
                    <span className="info-label">Base Price</span>
                    <span className="info-value">${currentContent.auctionDetails?.basePrice || 0}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaDollarSign className="info-icon" />
                  <div>
                    <span className="info-label">Minimum Bid Increment</span>
                    <span className="info-value">${currentContent.auctionDetails?.minimumBidIncrement || 0}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaClock className="info-icon" />
                  <div>
                    <span className="info-label">Start Date</span>
                    <span className="info-value">{formatDate(currentContent.auctionDetails?.auctionStartDate)}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaClock className="info-icon" />
                  <div>
                    <span className="info-label">End Date</span>
                    <span className="info-value">{formatDate(currentContent.auctionDetails?.auctionEndDate)}</span>
                  </div>
                </div>
                <div className="info-item">
                  <FaFlag className="info-icon" />
                  <div>
                    <span className="info-label">Allow Pre-auction Offers</span>
                    <span className="info-value">
                      {currentContent.auctionDetails?.allowOfferBeforeAuctionStart ? 'Yes' : 'No'}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Sales Information */}
          <div className="info-section">
            <h3>Sales Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <FaShoppingCart className="info-icon" />
                <div>
                  <span className="info-label">Sales Count</span>
                  <span className="info-value">{currentContent.salesCount || 0}</span>
                </div>
              </div>
              <div className="info-item">
                <FaDollarSign className="info-icon" />
                <div>
                  <span className="info-label">Total Revenue</span>
                  <span className="info-value">${currentContent.totalRevenue || 0}</span>
                </div>
              </div>
              <div className="info-item">
                <FaStar className="info-icon" />
                <div>
                  <span className="info-label">Average Rating</span>
                  <span className="info-value">{currentContent.averageRating || 0} ({currentContent.reviewCount} reviews)</span>
                </div>
              </div>
            </div>
          </div>

          {/* Status History */}
          <div className="info-section">
            <h3>
              <FaHistory className="section-icon" />
              Status History
              <button
                className="toggle-history-btn"
                onClick={() => setShowHistory(!showHistory)}
              >
                {showHistory ? 'Hide History' : 'Show History'}
              </button>
            </h3>
            {showHistory && (
              <div className="status-history-list">
                {currentContent.statusHistory?.map((history, index) => (
                  <div key={history.id} className="status-history-item">
                    <div className="status-badge">{getStatusBadge(history.status)}</div>
                    <div className="status-details">
                      <div className="status-timestamp">{formatDate(history.changedAt)}</div>
                      <div className="status-reason">{history.reason}</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentDetailModal;
