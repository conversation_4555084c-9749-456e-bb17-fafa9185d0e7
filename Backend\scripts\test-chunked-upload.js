/**
 * Test script for chunked upload functionality
 * This script tests the chunked upload endpoints to ensure they work correctly
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const axios = require('axios');
require('dotenv').config();

// Configuration
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:5000/api';
const TEST_FILE_SIZE = 50 * 1024 * 1024; // 50MB test file
const CHUNK_SIZE = 5 * 1024 * 1024; // 5MB chunks

// Test credentials (you'll need to replace these with valid credentials)
const TEST_USER = {
  email: '<EMAIL>',
  password: 'testpassword123'
};

/**
 * Create a test file of specified size
 */
function createTestFile(size, filename) {
  console.log(`Creating test file: ${filename} (${Math.round(size / (1024 * 1024))}MB)`);
  
  const buffer = Buffer.alloc(size);
  // Fill with random data
  for (let i = 0; i < size; i++) {
    buffer[i] = Math.floor(Math.random() * 256);
  }
  
  fs.writeFileSync(filename, buffer);
  console.log(`Test file created: ${filename}`);
  return filename;
}

/**
 * Authenticate and get JWT token
 */
async function authenticate() {
  try {
    console.log('Authenticating...');
    const response = await axios.post(`${API_BASE_URL}/auth/login`, TEST_USER);
    
    if (response.data.success && response.data.token) {
      console.log('Authentication successful');
      return response.data.token;
    } else {
      throw new Error('Authentication failed: No token received');
    }
  } catch (error) {
    console.error('Authentication error:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Initialize chunked upload
 */
async function initializeUpload(token, fileName, fileSize, fileType) {
  try {
    console.log('Initializing chunked upload...');
    
    const totalChunks = Math.ceil(fileSize / CHUNK_SIZE);
    
    const response = await axios.post(
      `${API_BASE_URL}/content/upload/init`,
      {
        fileName,
        fileSize,
        fileType,
        totalChunks
      },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.data.success) {
      console.log('Upload initialized:', response.data.data.uploadId);
      return response.data.data;
    } else {
      throw new Error('Upload initialization failed');
    }
  } catch (error) {
    console.error('Upload initialization error:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Upload a single chunk
 */
async function uploadChunk(token, uploadId, chunkIndex, chunkData) {
  try {
    const formData = new FormData();
    formData.append('chunk', chunkData, {
      filename: `chunk_${chunkIndex}`,
      contentType: 'application/octet-stream'
    });
    formData.append('uploadId', uploadId);
    formData.append('chunkIndex', chunkIndex.toString());
    
    const response = await axios.post(
      `${API_BASE_URL}/content/upload/chunk`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          ...formData.getHeaders()
        },
        timeout: 300000 // 5 minutes
      }
    );
    
    if (response.data.success) {
      console.log(`Chunk ${chunkIndex + 1} uploaded successfully`);
      return response.data.data;
    } else {
      throw new Error(`Chunk ${chunkIndex} upload failed`);
    }
  } catch (error) {
    console.error(`Chunk ${chunkIndex} upload error:`, error.response?.data || error.message);
    throw error;
  }
}

/**
 * Complete the upload
 */
async function completeUpload(token, uploadId) {
  try {
    console.log('Completing upload...');
    
    const response = await axios.post(
      `${API_BASE_URL}/content/upload/complete`,
      { uploadId },
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    if (response.data.success) {
      console.log('Upload completed successfully:', response.data.data.fileUrl);
      return response.data.data;
    } else {
      throw new Error('Upload completion failed');
    }
  } catch (error) {
    console.error('Upload completion error:', error.response?.data || error.message);
    throw error;
  }
}

/**
 * Main test function
 */
async function testChunkedUpload() {
  const testFileName = 'test-chunked-upload.bin';
  const testFilePath = path.join(__dirname, testFileName);
  
  try {
    console.log('=== Chunked Upload Test ===');
    
    // Create test file
    createTestFile(TEST_FILE_SIZE, testFilePath);
    
    // Authenticate
    const token = await authenticate();
    
    // Initialize upload
    const uploadData = await initializeUpload(
      token,
      testFileName,
      TEST_FILE_SIZE,
      'application/octet-stream'
    );
    
    // Read file and create chunks
    const fileBuffer = fs.readFileSync(testFilePath);
    const totalChunks = Math.ceil(TEST_FILE_SIZE / CHUNK_SIZE);
    
    console.log(`Uploading ${totalChunks} chunks...`);
    
    // Upload chunks
    for (let i = 0; i < totalChunks; i++) {
      const start = i * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, TEST_FILE_SIZE);
      const chunkData = fileBuffer.slice(start, end);
      
      await uploadChunk(token, uploadData.uploadId, i, chunkData);
    }
    
    // Complete upload
    const finalResult = await completeUpload(token, uploadData.uploadId);
    
    console.log('=== Test Completed Successfully ===');
    console.log('Final file URL:', finalResult.fileUrl);
    console.log('File size:', finalResult.fileSizeMB, 'MB');
    
  } catch (error) {
    console.error('=== Test Failed ===');
    console.error('Error:', error.message);
    process.exit(1);
  } finally {
    // Clean up test file
    if (fs.existsSync(testFilePath)) {
      fs.unlinkSync(testFilePath);
      console.log('Test file cleaned up');
    }
  }
}

/**
 * Test upload status endpoint
 */
async function testUploadStatus() {
  try {
    console.log('=== Testing Upload Status Endpoint ===');
    
    const token = await authenticate();
    
    // Try to get status for a non-existent upload
    try {
      await axios.get(
        `${API_BASE_URL}/content/upload/status/non-existent-id`,
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );
    } catch (error) {
      if (error.response?.status === 404) {
        console.log('✓ Correctly returned 404 for non-existent upload');
      } else {
        throw error;
      }
    }
    
    console.log('Upload status test completed');
    
  } catch (error) {
    console.error('Upload status test failed:', error.message);
  }
}

// Run tests
if (require.main === module) {
  console.log('Starting chunked upload tests...');
  console.log('API Base URL:', API_BASE_URL);
  console.log('Test file size:', Math.round(TEST_FILE_SIZE / (1024 * 1024)), 'MB');
  console.log('Chunk size:', Math.round(CHUNK_SIZE / (1024 * 1024)), 'MB');
  console.log('');
  
  testChunkedUpload()
    .then(() => testUploadStatus())
    .then(() => {
      console.log('All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Tests failed:', error.message);
      process.exit(1);
    });
}

module.exports = {
  testChunkedUpload,
  testUploadStatus,
  createTestFile
};
