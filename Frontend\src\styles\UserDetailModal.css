/* UserDetailModal Component Styles */
.UserDetailModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1500; /* Above navbar (1000) and sidebar (200) */
  display: flex;
  align-items: center;
  justify-content: center;
}

.UserDetailModal__overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.UserDetailModal__container {
  position: relative;
  width: 90%;
  max-width: 900px;
  max-height: 90vh;
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Header */
.UserDetailModal__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.UserDetailModal .header-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.UserDetailModal .user-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--heading4);
  overflow: hidden;
  flex-shrink: 0;
}

.UserDetailModal.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.UserDetailModal .user-basic-info h2 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading5);
  color: var(--secondary-color);
}

.UserDetailModal .user-badges {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.UserDetailModal .role-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  text-transform: capitalize;
}

.UserDetailModal .status-icon {
  font-size: var(--basefont);
}

.UserDetailModal .status-icon.active {
  color: #10b981;
}

.UserDetailModal .status-icon.inactive {
  color: #ef4444;
}

.UserDetailModal .status-icon.deleted {
  color: #6b7280;
}

.UserDetailModal .verification-icon {
  font-size: var(--basefont);
  color: #3b82f6;
}

.UserDetailModal .close-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background-color: var(--light-gray);
  color: var(--dark-gray);
  cursor: pointer;
  transition: all 0.3s ease;
}

.UserDetailModal .close-btn:hover {
  background-color: var(--btn-color);
  color: var(--white);
}

/* Content */
.UserDetailModal__content {
  flex: 1;
  padding: var(--heading6);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Sections */
.UserDetailModal .info-section,
.UserDetailModal .stats-section,
.UserDetailModal .activity-section,
.UserDetailModal .actions-section {
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.UserDetailModal .section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.UserDetailModal .section-header h3,
.UserDetailModal .stats-section h3,
.UserDetailModal .activity-section h3 {
  margin: 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

/* Info Grid */
.UserDetailModal .info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
  padding: 0 5px;
}

.UserDetailModal .info-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
}

.UserDetailModal .info-icon {
  color: var(--btn-color);
  font-size: var(--basefont);
  flex-shrink: 0;
}

.UserDetailModal .info-item div {
  display: flex;
  flex-direction: column;
}

.UserDetailModal .info-label {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  font-weight: 600;
}

.UserDetailModal .info-value {
  font-size: var(--smallfont);
  color: var(--text-color);
}

/* Edit Form */
.UserDetailModal .edit-form {
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.UserDetailModal .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.UserDetailModal .form-group {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.UserDetailModal .form-group label {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--secondary-color);
}

.UserDetailModal .form-input,
.UserDetailModal .form-select {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  transition: all 0.3s ease;
}

.UserDetailModal .form-input:focus,
.UserDetailModal .form-select:focus {
  outline: none;
  border-color: var(--btn-color);
  box-shadow: 0 0 0 3px rgba(238, 52, 37, 0.1);
}

.UserDetailModal .form-actions {
  display: flex;
  gap: var(--smallfont);
  justify-content: flex-end;
}

/* Stats Grid */
.UserDetailModal .stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--basefont);
}

.UserDetailModal .stat-card {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.UserDetailModal .stat-icon {
  width: 25px;
  height: 25px;
  padding: 5px;
  border-radius: 50%;
  background-color: var(--btn-color);
  color: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--heading6);
}

.UserDetailModal .stat-content {
  display: flex;
  flex-direction: column;
}

.UserDetailModal .stat-number {
  font-size: var(--heading5);
  font-weight: 700;
  color: var(--secondary-color);
}

.UserDetailModal .stat-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Activity List */
.UserDetailModal .activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
  max-height: 200px;
  overflow-y: auto;
}

.UserDetailModal .activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont);
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
}

.UserDetailModal .activity-content {
  display: flex;
  flex-direction: column;
}

.UserDetailModal .activity-description {
  font-size: var(--smallfont);
  color: var(--text-color);
}

.UserDetailModal .activity-date {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.UserDetailModal .activity-amount {
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--btn-color);
}

/* Action Buttons */
.UserDetailModal .action-buttons {
  display: flex;
  gap: var(--basefont);
  justify-content: center;
}

/* Buttons */
.UserDetailModal .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.UserDetailModal .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.UserDetailModal .btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.UserDetailModal .btn.btn-primary:hover:not(:disabled) {
  background-color: #d32f2f;
}

.UserDetailModal .btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.UserDetailModal .btn.btn-outline:hover:not(:disabled) {
  background-color: var(--bg-gray);
}

.UserDetailModal .btn.btn-success {
  background-color: #10b981;
  color: var(--white);
}

.UserDetailModal .btn.btn-success:hover:not(:disabled) {
  background-color: #059669;
}

.UserDetailModal .btn.btn-warning {
  background-color: #f59e0b;
  color: var(--white);
}

.UserDetailModal .btn.btn-warning:hover:not(:disabled) {
  background-color: #d97706;
}

.UserDetailModal .btn.btn-danger {
  background-color: var(--btn-color);
  color: var(--white);
}

.UserDetailModal .btn.btn-danger:hover:not(:disabled) {
  background-color: var(--btn-color);
}

/* Enhanced form validation styles */
.UserDetailModal .form-input.error,
.UserDetailModal .form-select.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.UserDetailModal .error-text {
  color: #ef4444;
  font-size: var(--extrasmallfont);
  margin-top: 0.25rem;
  display: block;
}

.UserDetailModal .error-message.general-error {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  padding: var(--smallfont);
  margin-bottom: var(--basefont);
  color: #dc2626;
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  font-size: var(--smallfont);
}

/* Loading spinner */
.UserDetailModal .spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Enhanced form labels */
.UserDetailModal .form-group label {
  font-weight: 600;
  margin-bottom: var(--smallfont);
  display: block;
  color: var(--secondary-color);
}

/* Disabled form elements */
.UserDetailModal .form-input:disabled,
.UserDetailModal .form-select:disabled {
  background-color: #f9fafb;
  cursor: not-allowed;
  opacity: 0.6;
}

/* Enhanced button loading states */
.UserDetailModal .btn .spinner {
  margin-right: var(--smallfont);
}

/* Delete confirmation modal */
.UserDetailModal .delete-confirm-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1600; /* Above the main modal */
}

.UserDetailModal .delete-confirm-modal {
  background: var(--white);
  border-radius: var(--border-radius-large);
  padding: var(--heading6);
  max-width: 400px;
  width: 90%;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.UserDetailModal .delete-confirm-header {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-bottom: var(--basefont);
}

.UserDetailModal .delete-confirm-header .warning-icon {
  color: #f59e0b;
  font-size: var(--heading5);
}

.UserDetailModal .delete-confirm-header h3 {
  margin: 0;
  color: var(--secondary-color);
  font-size: var(--heading6);
  font-weight: 600;
}

.UserDetailModal .delete-confirm-content {
  margin-bottom: var(--heading6);
}

.UserDetailModal .delete-confirm-content p {
  margin: 0 0 var(--smallfont) 0;
  color: var(--text-color);
  line-height: 1.5;
  font-size: var(--smallfont);
}

.UserDetailModal .delete-confirm-content .warning-text {
  color: #dc2626;
  font-weight: 500;
  font-size: var(--extrasmallfont);
}

.UserDetailModal .delete-confirm-actions {
  display: flex;
  gap: var(--smallfont);
  justify-content: flex-end;
}

.UserDetailModal .delete-confirm-actions .btn {
  min-width: 120px;
}

/* Enhanced action buttons */
.UserDetailModal .action-buttons .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all 0.2s ease;
}

.UserDetailModal .action-buttons .btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Enhanced form actions */
.UserDetailModal .form-actions {
  display: flex;
  gap: var(--smallfont);
  justify-content: flex-end;
  margin-top: var(--heading6);
  padding-top: var(--basefont);
  border-top: 1px solid var(--light-gray);
}

.UserDetailModal .form-actions .btn {
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--smallfont);
}

/* Responsive styles */
@media (max-width: 768px) {
  .UserDetailModal__container {
    width: 95%;
    max-height: 95vh;
  }

  .UserDetailModal__header {
    padding: var(--basefont);
  }

  .UserDetailModal .header-info {
    flex-direction: column;
    text-align: center;
  }

  .UserDetailModal .user-avatar {
    width: 60px;
    height: 60px;
    font-size: var(--heading5);
  }

  .UserDetailModal__content {
    padding: var(--basefont);
  }

  .UserDetailModal .info-grid,
  .UserDetailModal .form-row,
  .UserDetailModal .stats-grid {
    grid-template-columns: 1fr;
  }

  .UserDetailModal .action-buttons {
    flex-direction: column;
  }

  .UserDetailModal .form-actions {
    justify-content: stretch;
  }

  .UserDetailModal .form-actions .btn {
    flex: 1;
  }

  .UserDetailModal .delete-confirm-actions {
    flex-direction: column;
  }

  .UserDetailModal .delete-confirm-actions .btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .UserDetailModal__container {
    width: 100%;
    height: 100%;
    max-height: 100vh;
    border-radius: 0;
  }

  .UserDetailModal .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }
}
