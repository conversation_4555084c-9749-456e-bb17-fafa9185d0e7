/* AdminSidebar Component Styles */
.AdminSidebar {
  display: flex;
  flex-direction: column;
  background-color: var(--white);
  width: 100%;
  height: 100%;
  transition: width 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  position: relative;
}

.AdminSidebar__container {
  display: flex;
  flex-direction: column;
  padding: var(--basefont);
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

/* Logo Section */
.AdminSidebar__logo {
  text-align: center;
  padding: var(--heading6) 0;
  border-bottom: 1px solid var(--light-gray);
  margin-bottom: var(--heading6);
}
.AdminSidebar__logo .logo-text{
  display: flex;
  justify-content: space-around;
  gap:10px
}
.AdminSidebar__logo h3 {
  color: var(--primary-color);
  font-size: var(--heading5);
  font-weight: 700;
  margin: 0;
}

/* Mobile Header Section */
.AdminSidebar__mobile-header {
  display: none;
}

.AdminSidebar__mobile-header .AdminSidebar__logo {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  text-align: left;
  padding: var(--basefont);
  border-bottom: 1px solid var(--bg-gray);
  margin-bottom: var(--basefont);
}

.AdminSidebar__mobile-header .AdminSidebar__logo img {
  width: 50px;
  height: 50px;
  object-fit: contain;
  border-radius: var(--border-radius);
}

.AdminSidebar__mobile-header .logo-text h3 {
  font-size: var(--heading6);
  margin: 0 0 2px 0;
  color: var(--primary-color);
}

.AdminSidebar__mobile-header .logo-text span {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
}

/* Mobile Profile Section */
.AdminSidebar__mobile-profile {
  display: none;
  padding: 0 var(--basefont) var(--basefont) var(--basefont);
  border-bottom: 1px solid var(--bg-gray);
  margin-bottom: var(--basefont);
}

.profile-card {
  display: flex;
  align-items: center;
  gap: var(--basefont);
  padding: var(--basefont);
 
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
}

.profile-card:hover {
  background-color: var(--light-gray);
  transform: translateY(-1px);
}

.profile-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--bg-blue);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--btn-color);
  font-size: var(--heading6);
  overflow: hidden;
  flex-shrink: 0;
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.profile-details h4 {
  margin: 0 0 2px 0;
  font-size: var(--smallfont);
  font-weight: 600;
  color: var(--text-color);
}

.profile-details p {
  margin: 0 0 4px 0;
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.profile-role {
  font-size: var(--extrasmallfont);
  color: var(--btn-color);
  font-weight: 500;
  background-color: rgba(238, 52, 37, 0.1);
  padding: 2px 6px;
  border-radius: 12px;
}

/* Mobile Actions Section */
.AdminSidebar__mobile-actions {
  display: none;
  margin-top: auto;
  padding-top: var(--basefont);
  border-top: 1px solid var(--bg-gray);
}

.AdminSidebar__mobile-actions .AdminSidebar__item {
  margin-bottom: var(--smallfont);
}

.AdminSidebar__mobile-actions .settings-item {
  color: var(--secondary-color);
}

.AdminSidebar__mobile-actions .settings-item:hover {
  background-color: var(--bg-gray);
  color: var(--primary-color);
}

.AdminSidebar__logo span {
  color: var(--dark-gray);
  font-size: var(--smallfont);
  font-weight: 500;
}

/* Navigation Menu */
.AdminSidebar__menu {
  display: flex;
  flex-direction: column;
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
  gap: 4px;
}

.AdminSidebar__item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: var(--basefont);
  position: relative;
}

.AdminSidebar__item .item-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.AdminSidebar__item:hover {
  background-color: var(--bg-gray);
  color: var(--btn-color);
}

.AdminSidebar__item.active {
  background-color: var(--btn-color);
  color: var(--white);
  font-weight: 600;
}

/* Disabled state */
.AdminSidebar__item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  color: var(--dark-gray);
}

.AdminSidebar__item.disabled:hover {
  background-color: transparent;
  color: var(--dark-gray);
  cursor: not-allowed;
}

/* Coming Soon Badge */
.coming-soon-badge {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: var(--white);
  font-size: 9px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  margin-left: 8px;
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(238, 90, 36, 0.3);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 1px 3px rgba(238, 90, 36, 0.3);
  }
  50% {
    box-shadow: 0 1px 6px rgba(238, 90, 36, 0.5);
  }
  100% {
    box-shadow: 0 1px 3px rgba(238, 90, 36, 0.3);
  }
}

.AdminSidebar__icon {
  margin-right: var(--smallfont);
  font-size: var(--heading6);
  min-width: 20px;
}

/* Logout Section */
.AdminSidebar__logout {
  margin-top: auto;
  border-top: 1px solid var(--light-gray);
  padding-top: var(--basefont);
}

.AdminSidebar__logout .logout-item {
  color: var(--btn-color);
  font-weight: 600;
}

.AdminSidebar__logout .logout-item:hover {
  background-color: rgba(238, 52, 37, 0.1);
  color: var(--btn-color);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminSidebar__item {
    padding: var(--smallfont) var(--basefont);
  }

  .AdminSidebar__item span {
    font-size: var(--smallfont);
  }

  .coming-soon-badge {
    font-size: 8px;
    padding: 1px 4px;
    margin-left: 6px;
  }
}

@media (max-width: 768px) {
  .AdminSidebar {
    /* Mobile sidebar is handled by AdminLayout positioning */
    background-color: var(--white);
    box-shadow: 4px 0 12px rgba(0, 0, 0, 0.15);
    overflow-y: auto;
  }

  .AdminSidebar__container {
    width: 100%;
    height: 100%;
    background-color: var(--white);
    overflow-y: auto;
    padding: 0;
    -webkit-overflow-scrolling: touch;
    display: flex;
    flex-direction: column;
  }

  /* Hide desktop logo, show mobile header */
  .AdminSidebar__logo {
    display: none;
  }

  .AdminSidebar__mobile-header {
    display: block;
  }

  .AdminSidebar__mobile-profile {
    display: block;
  }

  .AdminSidebar__mobile-actions {
    display: block;
  }

  /* Adjust menu spacing */
  .AdminSidebar__menu {
    flex: 1;
    padding: 0 var(--basefont);
  }

  .coming-soon-badge {
    font-size: 7px;
    padding: 1px 3px;
    margin-left: 4px;
  }

  /* Mobile header adjustments */
  .AdminSidebar__mobile-header .AdminSidebar__logo img {
    width: 45px;
    height: 45px;
  }

  .AdminSidebar__mobile-header .logo-text h3 {
    font-size: var(--heading6);
  }

  .AdminSidebar__mobile-header .logo-text span {
    font-size: var(--extrasmallfont);
  }

  /* Mobile profile adjustments */
  .profile-avatar {
    width: 42px;
    height: 42px;
  }

  .profile-details h4 {
    font-size: var(--extrasmallfont);
  }

  .profile-details p {
    font-size: 10px;
  }

  .AdminSidebar__item {
    padding: var(--basefont);
    margin-bottom: 4px;
    border-radius: var(--border-radius);
  }

  .AdminSidebar__item:hover {
    background-color: var(--bg-gray);
  }

  .AdminSidebar__item.active {
    background-color: var(--btn-color);
    color: var(--white);
    font-weight: 600;
  }

  .AdminSidebar__item span {
    font-size: var(--smallfont);
    font-weight: 500;
  }

  .AdminSidebar__icon {
    font-size: var(--basefont);
    margin-right: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .AdminSidebar__container {
    padding: 0;
  }

  /* Mobile header for small screens */
  .AdminSidebar__mobile-header .AdminSidebar__logo {
    padding: var(--smallfont);
  }

  .AdminSidebar__mobile-header .AdminSidebar__logo img {
    width: 40px;
    height: 40px;
  }

  .AdminSidebar__mobile-header .logo-text h3 {
    font-size: var(--smallfont);
  }

  /* Mobile profile for small screens */
  .AdminSidebar__mobile-profile {
    padding: 0 var(--smallfont) var(--smallfont) var(--smallfont);
  }

  .profile-card {
    padding: var(--smallfont);
  }

  .profile-avatar {
    width: 36px;
    height: 36px;
  }

  .profile-details h4 {
    font-size: 11px;
  }

  .profile-details p {
    font-size: 9px;
  }

  .profile-role {
    font-size: 8px;
    padding: 1px 4px;
  }

  /* Menu adjustments */
  .AdminSidebar__menu {
    padding: 0 var(--smallfont);
  }

  .AdminSidebar__item {
    padding: var(--smallfont) var(--basefont);
    margin-bottom: 2px;
  }

  .AdminSidebar__item span {
    font-size: var(--extrasmallfont);
  }

  .AdminSidebar__icon {
    font-size: var(--smallfont);
    margin-right: 8px;
  }

  /* Mobile actions for small screens */
  .AdminSidebar__mobile-actions {
    padding: var(--smallfont);
  }
}

/* Collapsible sidebar for desktop */
.AdminSidebar.collapsed {
  width: 80px; /* Slightly wider for better icon visibility */
}

.AdminSidebar.collapsed .AdminSidebar__logo span,
.AdminSidebar.collapsed .AdminSidebar__item span {
  display: none;
}

.AdminSidebar.collapsed .coming-soon-badge {
  display: none;
}

.AdminSidebar.collapsed .AdminSidebar__logo h3 {
  font-size: var(--smallfont);
  writing-mode: vertical-rl;
  text-orientation: mixed;
}

.AdminSidebar.collapsed .AdminSidebar__item {
  justify-content: center;
  padding: var(--basefont) var(--smallfont);
  position: relative;
}

.AdminSidebar.collapsed .AdminSidebar__icon {
  margin-right: 0;
}

/* Tooltip for collapsed sidebar */
.AdminSidebar.collapsed .AdminSidebar__item:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: var(--dark-gray);
  color: var(--white);
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  white-space: nowrap;
  z-index: 1200;
  margin-left: 8px;
  opacity: 0;
  animation: fadeIn 0.2s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Enhanced smooth transitions */
.AdminSidebar__logo span,
.AdminSidebar__item span {
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.AdminSidebar.collapsed .AdminSidebar__logo span,
.AdminSidebar.collapsed .AdminSidebar__item span {
  opacity: 0;
}

/* Scrollbar styling for fixed sidebar */
.AdminSidebar__container::-webkit-scrollbar {
  width: 6px;
}

.AdminSidebar__container::-webkit-scrollbar-track {
  background: var(--bg-gray);
  border-radius: 3px;
}

.AdminSidebar__container::-webkit-scrollbar-thumb {
  background: var(--light-gray);
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.AdminSidebar__container::-webkit-scrollbar-thumb:hover {
  background: var(--dark-gray);
}

/* Active state enhancements */
.AdminSidebar__item.active .AdminSidebar__icon {
  transform: scale(1.1);
}

/* Hover effects */
.AdminSidebar__item:not(.active):hover .AdminSidebar__icon {
  transform: scale(1.05);
}

/* Focus states for accessibility */
.AdminSidebar__item:focus {
  outline: 2px solid var(--btn-color);
  outline-offset: 2px;
}

/* Custom scrollbar for mobile */
@media (max-width: 768px) {
  .AdminSidebar__container::-webkit-scrollbar {
    width: 4px;
  }

  .AdminSidebar__container::-webkit-scrollbar-track {
    background: var(--bg-gray);
  }

  .AdminSidebar__container::-webkit-scrollbar-thumb {
    background: var(--light-gray);
    border-radius: 2px;
  }

  .AdminSidebar__container::-webkit-scrollbar-thumb:hover {
    background: var(--dark-gray);
  }
}
