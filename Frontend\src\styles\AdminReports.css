/**/
/* AdminReports Component Styles */
.AdminReports {
  display: flex;
  flex-direction: column;
  gap: var(--heading6);
}

/* Header */
.AdminReports .AdminReports__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--heading6);
  margin-bottom: var(--basefont);
}

.AdminReports .header-left {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.AdminReports .period-selector {
  display: flex;
  align-items: center;
  border: 1px solid var(--light-gray);

  border-radius: var(--border-radius);
  padding: 0 8px;
}

.AdminReports .calendar-icon {
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.AdminReports .period-select {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  background-color: var(--white);
  cursor: pointer;
}

.AdminReports .period-select:focus {
  outline: none;
  border-color: var(--btn-color);
}

.AdminReports .header-right {
  display: flex;
  gap: var(--smallfont);
}

.AdminReports .btn {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.AdminReports .btn.btn-primary {
  background-color: var(--btn-color);
  color: var(--white);
}

.AdminReports .btn.btn-primary:hover {
  background-color: #d32f2f;
}

.AdminReports .btn.btn-outline {
  background-color: transparent;
  color: var(--secondary-color);
  border: 1px solid var(--light-gray);
}

.AdminReports .btn.btn-outline:hover {
  background-color: var(--bg-gray);
}

/* Metrics Cards */
.AdminReports .AdminReports__metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--heading6);
  margin-bottom: var(--heading6);
}

.AdminReports .metric-card {
  background-color: var(--white);
  border-radius: var(--border-radius);
  padding: 5px var(--heading6);
  box-shadow: var(--box-shadow-light);
  border-left: 4px solid;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.AdminReports .metric-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.AdminReports .metric-card.revenue {
  border-left-color: var(--btn-color);
}

.AdminReports .metric-card.users {
  border-left-color: #3b82f6;
}

.AdminReports .metric-card.content {
  border-left-color: #10b981;
}

.AdminReports .metric-card.monthly {
  border-left-color: #f59e0b;
}

.AdminReports .metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.AdminReports .metric-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  font-size: var(--heading5);
  color: var(--white);
}

.AdminReports .metric-card.revenue .metric-icon {
  background-color: var(--btn-color);
}

.AdminReports .metric-card.users .metric-icon {
  background-color: #3b82f6;
}

.AdminReports .metric-card.content .metric-icon {
  background-color: #10b981;
}

.AdminReports .metric-card.monthly .metric-icon {
  background-color: #f59e0b;
}

.AdminReports .metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: var(--smallfont);
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 12px;
}

.AdminReports .metric-trend.positive {
  background-color: #dcfce7;
  color: #166534;
}

.AdminReports .metric-trend.negative {
  background-color: #fef2f2;
  color: #991b1b;
}

.AdminReports .metric-content {
  text-align: left;
  display: flex;
  justify-content: space-between;
  gap: 10px;
  align-items: center;
}

.AdminReports .metric-number {
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: 4px;
}

.AdminReports .metric-label {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
  margin-bottom: 2px;
}

.AdminReports .metric-sublabel {
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

/* Charts Section */
.AdminReports .AdminReports__charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading6);
  margin-bottom: var(--heading6);
}

.AdminReports .chart-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  border: 1px solid var(--light-gray);
  overflow: hidden;
}

.AdminReports .chart-header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.AdminReports .chart-header h3 {
  margin: 0 0 4px 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.AdminReports .chart-header p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.AdminReports .chart-placeholder {
  padding: var(--heading6);
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.AdminReports .chart-mock {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.AdminReports .chart-bars {
  display: flex;
  align-items: end;
  justify-content: space-around;
  height: 80%;
  gap: var(--smallfont);
}

.AdminReports .chart-bar {
  flex: 1;
  background-color: var(--btn-color);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  position: relative;
  min-height: 20px;
  transition: all 0.3s ease;
  display: flex;
  align-items: end;
  justify-content: center;
}

.AdminReports .chart-bar.users-bar {
  background-color: #3b82f6;
}

.AdminReports .chart-bar:hover {
  opacity: 0.8;
}

.AdminReports .bar-value {
  position: absolute;
  top: -25px;
  font-size: var(--extrasmallfont);
  font-weight: 600;
  color: var(--secondary-color);
  white-space: nowrap;
}

.AdminReports .chart-labels {
  display: flex;
  justify-content: space-around;
  margin-top: var(--smallfont);
  height: 20%;
}

.AdminReports .chart-label {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  text-align: center;
}

/* Distribution Charts */
.AdminReports .AdminReports__distributions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--heading6);
}

.AdminReports .distribution-container {
  background-color: var(--white);
  border-radius: var(--border-radius);
  border: 1px solid var(--light-gray);
  overflow: hidden;
}

.AdminReports .distribution-header {
  padding: var(--heading6);
  border-bottom: 1px solid var(--light-gray);
  background-color: var(--bg-gray);
}

.AdminReports .distribution-header h3 {
  margin: 0 0 4px 0;
  font-size: var(--heading6);
  color: var(--secondary-color);
}

.AdminReports .distribution-header p {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.AdminReports .distribution-chart {
  padding: var(--heading6);
  display: flex;
  flex-direction: column;
  gap: var(--basefont);
}

.AdminReports .distribution-item {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.AdminReports .distribution-bar {
  flex: 1;
  height: 20px;
  background-color: var(--bg-gray);
  border-radius: 10px;
  overflow: hidden;
}

.AdminReports .distribution-fill {
  height: 100%;
  background-color: var(--btn-color);
  border-radius: 10px;
  transition: width 0.3s ease;
}

.AdminReports .distribution-fill.revenue-fill {
  background-color: #10b981;
}

.AdminReports .distribution-info {
  display: flex;
  flex-direction: column;
  min-width: 120px;
}

.AdminReports .distribution-label {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  font-weight: 600;
}

.AdminReports .distribution-value {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .AdminReports .AdminReports__charts,
  .AdminReports .AdminReports__distributions {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .AdminReports .AdminReports__header {
    flex-direction: column;
    align-items: stretch;
  }

  .AdminReports .header-right {
    justify-content: center;
  }

  .AdminReports .AdminReports__metrics {
    grid-template-columns: 1fr 1fr;
  }

  .AdminReports .metric-card {
    padding: var(--basefont);
  }

  .AdminReports .metric-icon {
    width: 40px;
    height: 40px;
    font-size: var(--heading6);
  }

  .AdminReports .metric-number {
    font-size: var(--heading4);
  }

  .AdminReports .chart-placeholder {
    height: 250px;
  }

  .AdminReports .distribution-info {
    min-width: 100px;
  }
}

@media (max-width: 480px) {
  .AdminReports .AdminReports__metrics {
    grid-template-columns: 1fr;
  }

  .AdminReports .metric-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .AdminReports .chart-placeholder {
    height: 200px;
    padding: var(--basefont);
  }

  .AdminReports .bar-value {
    display: none;
  }

  .AdminReports .distribution-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--smallfont);
  }

  .AdminReports .distribution-info {
    min-width: auto;
    flex-direction: row;
    justify-content: space-between;
  }
}
