import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { FaExclamationTriangle, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import '../../styles/UniversalPDFViewer.css';
import { IMAGE_BASE_URL } from '../../utils/constants';

// Set worker source directly to CDN with fallback
pdfjs.GlobalWorkerOptions.workerSrc = `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

const UniversalPDFViewer = ({
  fileUrl,
  fileName = '',
  title = 'PDF Document',
  className = '',
  height = '100%',
  showDownload = false,
  onDownload = null,
  showNativeOptions = false
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [pageWidth, setPageWidth] = useState(null);
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [useIframeFallback, setUseIframeFallback] = useState(false);
  const containerRef = useRef(null);



  useEffect(() => {
    const updatePageWidth = () => {
      if (window.innerWidth < 768) {
        setPageWidth(window.innerWidth - 32);
      } else {
        setPageWidth(Math.min(800, window.innerWidth - 64));
      }
    };

    updatePageWidth();
    window.addEventListener('resize', updatePageWidth);

    return () => {
      window.removeEventListener('resize', updatePageWidth);
    };
  }, []);

  // Add mouse wheel event handling for better scrolling
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleWheel = (e) => {
      // Allow normal scrolling behavior
      e.stopPropagation();
    };

    container.addEventListener('wheel', handleWheel, { passive: true });

    return () => {
      if (container) {
        container.removeEventListener('wheel', handleWheel);
      }
    };
  }, []);

  const onDocumentLoadSuccess = useCallback(({ numPages }) => {
    setIsLoading(false);
    setHasError(false);
    setNumPages(numPages);
  }, []);

  const onDocumentLoadError = useCallback((error) => {
    console.error('PDF load error:', error);
    setIsLoading(false);
    setUseIframeFallback(true);
    // Don't set hasError immediately, let iframe try first
  }, []);

  const handlePrevPage = useCallback(() => {
    setPageNumber(prev => Math.max(prev - 1, 1));
  }, []);

  const handleNextPage = useCallback(() => {
    setPageNumber(prev => Math.min(prev + 1, numPages || 1));
  }, [numPages]);

  // Validate and prepare the file URL
  const validatedFileUrl = useMemo(() => {
    if (!fileUrl) {
      console.warn('[UniversalPDFViewer] No file URL provided');
      return null;
    }

    // Check if it's a valid URL
    try {
      if (fileUrl.startsWith('http://') || fileUrl.startsWith('https://')) {
        new URL(fileUrl); // This will throw if invalid
        return fileUrl;
      } else if (fileUrl.startsWith('/')) {
        // Relative URL, construct full URL
        return `${IMAGE_BASE_URL}${fileUrl}`;
      } else {
        console.warn('[UniversalPDFViewer] Invalid file URL format:', fileUrl);
        return null;
      }
    } catch (error) {
      console.error('[UniversalPDFViewer] Invalid URL:', fileUrl, error);
      return null;
    }
  }, [fileUrl]);

  // Memoize the file prop to prevent unnecessary re-renders
  const fileConfig = useMemo(() => {
    if (!validatedFileUrl) {
      return null;
    }

    return {
      url: validatedFileUrl,
      httpHeaders: {},
      withCredentials: false
    };
  }, [validatedFileUrl]);

  // Memoize the options prop to prevent unnecessary re-renders
  const pdfOptions = useMemo(() => ({
    cMapUrl: 'https://unpkg.com/pdfjs-dist@3.11.174/cmaps/',
    cMapPacked: true,
    standardFontDataUrl: 'https://unpkg.com/pdfjs-dist@3.11.174/standard_fonts/',
  }), []);

  // Main PDF viewer with react-pdf
  return (
    <div className={`universal-pdf-viewer ${className}`} style={{ height }}>
      {/* Pagination Controls */}
      {numPages > 1 && (
        <div className="universal-pdf-viewer__controls">
          <button
            className="universal-pdf-viewer__nav-btn"
            onClick={handlePrevPage}
            disabled={pageNumber <= 1}
          >
            <FaChevronLeft />
          </button>
          <span className="universal-pdf-viewer__page-info">
            Page {pageNumber} of {numPages}
          </span>
          <button
            className="universal-pdf-viewer__nav-btn"
            onClick={handleNextPage}
            disabled={pageNumber >= numPages}
          >
            <FaChevronRight />
          </button>
        </div>
      )}

      <div className="universal-pdf-viewer__document" ref={containerRef}>
        {isLoading && (
          <div className="universal-pdf-viewer__loading">
            <div className="universal-pdf-viewer__spinner"></div>
            <p>Loading PDF...</p>
          </div>
        )}

        {!validatedFileUrl ? (
          <div className="universal-pdf-viewer__error">
            <FaExclamationTriangle className="universal-pdf-viewer__warning-icon" />
            <h3>Invalid File URL</h3>
            <p>The file URL is not valid or accessible</p>
          </div>
        ) : hasError ? (
          <div className="universal-pdf-viewer__error">
            <FaExclamationTriangle className="universal-pdf-viewer__warning-icon" />
            <h3>PDF Preview Not Available</h3>
            <p>Unable to load the PDF preview</p>
          </div>
        ) : useIframeFallback ? (
          <div className="universal-pdf-viewer__iframe-fallback">
            {/* <div style={{ marginBottom: '10px', fontSize: '12px', color: '#666' }}>
              Using fallback PDF viewer
            </div> */}
            <iframe
              src={`${validatedFileUrl}#toolbar=0&navpanes=0&scrollbar=1&view=FitH`}
              style={{
                width: '100%',
                height: '100%',
                border: 'none',
                minHeight: '600px'
              }}
              title={title}
              onLoad={() => { }}
              onError={() => {
                setHasError(true);
                setUseIframeFallback(false);
              }}
            />
          </div>
        ) : fileConfig ? (
          <Document
            file={fileConfig}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            loading=""
            error=""
            options={pdfOptions}
          >
            <Page
              pageNumber={pageNumber}
              width={pageWidth}
              renderTextLayer={true}
              renderAnnotationLayer={true}
            />
          </Document>
        ) : (
          <div className="universal-pdf-viewer__error">
            <FaExclamationTriangle className="universal-pdf-viewer__warning-icon" />
            <h3>PDF Configuration Error</h3>
            <p>Unable to configure PDF viewer with the provided URL</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default UniversalPDFViewer; 