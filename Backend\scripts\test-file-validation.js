/**
 * Test script for file validation in chunked uploads
 * This script tests the file validation logic to ensure it works correctly
 */

const { validateFileForChunkedUpload } = require('../utils/fileUpload');

console.log('=== File Validation Test ===\n');

// Test cases
const testCases = [
  // Valid video files
  {
    fileName: 'test-video.mp4',
    fileSize: 500 * 1024 * 1024, // 500MB
    mimeType: 'video/mp4',
    expected: true,
    description: 'Valid MP4 video file'
  },
  {
    fileName: 'test-video.mov',
    fileSize: 800 * 1024 * 1024, // 800MB
    mimeType: 'video/quicktime',
    expected: true,
    description: 'Valid MOV video file'
  },
  {
    fileName: 'test-video.avi',
    fileSize: 300 * 1024 * 1024, // 300MB
    mimeType: 'video/x-msvideo',
    expected: true,
    description: 'Valid AVI video file'
  },
  {
    fileName: 'test-video.webm',
    fileSize: 200 * 1024 * 1024, // 200MB
    mimeType: 'video/webm',
    expected: true,
    description: 'Valid WEBM video file'
  },
  
  // Valid document files
  {
    fileName: 'test-document.pdf',
    fileSize: 10 * 1024 * 1024, // 10MB
    mimeType: 'application/pdf',
    expected: true,
    description: 'Valid PDF document'
  },
  
  // Valid image files
  {
    fileName: 'test-image.jpg',
    fileSize: 2 * 1024 * 1024, // 2MB
    mimeType: 'image/jpeg',
    expected: true,
    description: 'Valid JPEG image'
  },
  
  // Invalid cases - file too large
  {
    fileName: 'huge-video.mp4',
    fileSize: 2 * 1024 * 1024 * 1024, // 2GB
    mimeType: 'video/mp4',
    expected: false,
    description: 'Video file too large (>1GB)'
  },
  {
    fileName: 'huge-document.pdf',
    fileSize: 100 * 1024 * 1024, // 100MB
    mimeType: 'application/pdf',
    expected: false,
    description: 'Document file too large (>50MB)'
  },
  
  // Invalid cases - wrong extension
  {
    fileName: 'video-with-wrong-ext.txt',
    fileSize: 100 * 1024 * 1024, // 100MB
    mimeType: 'video/mp4',
    expected: false,
    description: 'Video with wrong file extension'
  },
  {
    fileName: 'document-with-wrong-ext.doc',
    fileSize: 10 * 1024 * 1024, // 10MB
    mimeType: 'application/pdf',
    expected: false,
    description: 'PDF with wrong file extension'
  },
  
  // Invalid cases - wrong MIME type
  {
    fileName: 'fake-video.mp4',
    fileSize: 100 * 1024 * 1024, // 100MB
    mimeType: 'application/octet-stream',
    expected: false,
    description: 'MP4 file with generic MIME type'
  },
  
  // Invalid cases - unsupported formats
  {
    fileName: 'unsupported.mkv',
    fileSize: 100 * 1024 * 1024, // 100MB
    mimeType: 'video/x-matroska',
    expected: false,
    description: 'Unsupported video format (MKV)'
  },
  {
    fileName: 'unsupported.docx',
    fileSize: 10 * 1024 * 1024, // 10MB
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    expected: false,
    description: 'Unsupported document format (DOCX)'
  }
];

// Run tests
let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  console.log(`Test ${index + 1}: ${testCase.description}`);
  console.log(`  File: ${testCase.fileName}`);
  console.log(`  Size: ${Math.round(testCase.fileSize / (1024 * 1024))}MB`);
  console.log(`  MIME: ${testCase.mimeType}`);
  
  try {
    const result = validateFileForChunkedUpload(
      testCase.fileName,
      testCase.fileSize,
      testCase.mimeType
    );
    
    const success = result.isValid === testCase.expected;
    
    if (success) {
      console.log(`  ✅ PASS - ${result.message}`);
      passed++;
    } else {
      console.log(`  ❌ FAIL - Expected ${testCase.expected ? 'valid' : 'invalid'}, got ${result.isValid ? 'valid' : 'invalid'}`);
      console.log(`  Message: ${result.message}`);
      failed++;
    }
  } catch (error) {
    console.log(`  ❌ ERROR - ${error.message}`);
    failed++;
  }
  
  console.log('');
});

// Summary
console.log('=== Test Summary ===');
console.log(`Total tests: ${testCases.length}`);
console.log(`Passed: ${passed}`);
console.log(`Failed: ${failed}`);
console.log(`Success rate: ${Math.round((passed / testCases.length) * 100)}%`);

if (failed === 0) {
  console.log('\n🎉 All tests passed!');
  process.exit(0);
} else {
  console.log(`\n💥 ${failed} test(s) failed!`);
  process.exit(1);
}
