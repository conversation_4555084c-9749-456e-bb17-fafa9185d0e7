
/* Controls Section */
.admin-offer-management .controls-section {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
  
    justify-content: space-between;
}
.admin-offer-management .search-box{
  width: 100%;
  max-width: 500px;
}
.admin-offer-management .search-box,
.admin-offer-management .filter-box {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #fff;
  padding: 8px 12px;
  border-radius: var(--border-radius);
  border: 1px solid #e0e0e0;
  color: #64748b;
}

.admin-offer-management .search-box input {
  border: none;
  outline: none;
  padding: 5px;
  width: 250px;
}

.admin-offer-management .filter-box select {
  border: none;
  outline: none;
  padding: 5px;
  min-width: 150px;
}

/* Bulk Actions */
.admin-offer-management .bulk-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  align-items: center;
}

.admin-offer-management .bulk-actions span {
  margin-right: 20px;
  color: #666;
}

.admin-offer-management .bulk-actions button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  padding: 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.admin-offer-management .bulk-actions button:hover {
  opacity: 0.9;
}

.admin-offer-management .bulk-actions button.approve {
  background: #28a745;
  color: white;
}

.admin-offer-management .bulk-actions button.reject {
  background: #ffc107;
  color: #000;
}

.admin-offer-management .bulk-actions button.delete {
  background: #dc3545;
  color: white;
}

/* Table Styles */
.admin-offer-management .table-container {
  background: #fff;
  overflow-x: auto;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
}

.admin-offer-management table {
  width: 100%;
  border-collapse: collapse;
}

.admin-offer-management th,
.admin-offer-management td {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  
}

.admin-offer-management th {
  background: #f8f9fa;
  font-weight: 600;
  color: #333;
}

/* Content Cell */
.admin-offer-management .content-cell {
  display: flex;
  flex-direction: column;
}

.admin-offer-management .content-cell .title {
  font-weight: 500;
  color: #333;
}

.admin-offer-management .content-cell .details {
  font-size: 0.85em;
  color: #666;
}

/* User Cell */
.admin-offer-management .user-cell {
  display: flex;
  align-items: center;
  gap: 10px;
}

.admin-offer-management .user-cell .profile-image {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  object-position: center;
  flex-shrink: 0;
}

.admin-offer-management .user-cell .user-info {
  display: flex;
  flex-direction: column;

}

.admin-offer-management .user-cell .name {
  font-weight: 500;
  color: #333;
}

.admin-offer-management .user-cell .email {
  font-size: 0.85em;
  color: #666;
}

/* Message Cell */
.admin-offer-management .message-cell {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
}

/* Status Badge */
.admin-offer-management .status-badge {
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: 0.85em;
  font-weight: 500;
}

.admin-offer-management .status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.admin-offer-management .status-badge.accepted {
  background: #d4edda;
  color: #155724;
}

.admin-offer-management .status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

.admin-offer-management .status-badge.expired {
  background-color: #fef2f2;
    color: #991b1b;
}

/* Action Buttons */
.admin-offer-management .actions {
  display: flex;
  gap: 6px;
}

.admin-offer-management .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  padding: 0;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  color: white;
}

.admin-offer-management .action-btn:hover {
  opacity: 0.9;
}

.admin-offer-management .action-btn.view {
  background: #17a2b8;
}

.admin-offer-management .action-btn.approve {
  background: #28a745;
}

.admin-offer-management .action-btn.reject {
  background: #ffc107;
  color: #000;
}

.admin-offer-management .action-btn.delete {
  background: #dc3545;
}

/* Loading and No Data States */
.admin-offer-management .loading,
.admin-offer-management .no-data {
  text-align: center;
  padding: 40px;
  color: #666;
}

/* Pagination */
.admin-offer-management .pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
  padding: 20px 0;
}

.admin-offer-management .pagination button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.admin-offer-management .pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.admin-offer-management .pagination button:not(:disabled):hover {
  background: #e9ecef;
}

.admin-offer-management .pagination span {
  color: #666;
}
.admin-offer-management .content-info{
  display: flex;
  align-items: center;
  gap: 10px;
}
.admin-offer-management .content-thumbnail {
    border-radius: 8px;
    overflow: hidden;
 
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}
@media (max-width: 768px) {
  .admin-offer-management .controls-section {
flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}
.admin-offer-management .filter-box {
  width: fit-content;
}
}